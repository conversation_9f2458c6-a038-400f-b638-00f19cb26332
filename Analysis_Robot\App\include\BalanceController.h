#ifndef BALANCE_CONTROLLER_H
#define BALANCE_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <string>
#include <mutex>
#include <map>
#include <nlohmann/json.hpp>


#include "BalanceDriver.h"

/**
 * @brief 天平控制器类
 * 
 * 实现天平设备的REST接口控制，包括：
 * - 称重操作
 * - 去皮操作
 * - 状态查询
 */
class BalanceController : public AnalysisRobot::RestInterface::IDeviceController {
public:
    /**
     * @brief 构造函数
     * @param driver 天平驱动实例
     */
    explicit BalanceController(std::shared_ptr<AnalysisRobot::Balance::BalanceDriver> driver);
    
    /**
     * @brief 析构函数
     */
    ~BalanceController();
    
    // 实现IDeviceController接口
    AnalysisRobot::RestInterface::DeviceStatus getStatus() override;
    AnalysisRobot::RestInterface::TaskInfo executeOperation(const nlohmann::json& request) override;
    AnalysisRobot::RestInterface::TaskInfo queryTask(int taskId) override;

private:
    std::shared_ptr<AnalysisRobot::Balance::BalanceDriver> m_driver;    // 天平驱动
    std::map<int, AnalysisRobot::RestInterface::TaskInfo> m_tasks;      // 任务存储
    std::mutex m_tasksMutex;                                            // 任务锁
    int m_nextTaskId;                                                   // 下一个任务ID
    
    /**
     * @brief 处理称重操作
     * @param data 请求数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleWeighOperation(const nlohmann::json& data);
    
    /**
     * @brief 处理去皮操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleTareOperation();
    
    /**
     * @brief 处理复位操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleResetOperation();
    
    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSuccessTask(const std::string& action, const nlohmann::json& data = nlohmann::json());
    
    /**
     * @brief 创建错误任务
     * @param action 操作名称
     * @param error 错误信息
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createErrorTask(const std::string& action, const std::string& error);
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString() const;
    

};

#endif // BALANCE_CONTROLLER_H
