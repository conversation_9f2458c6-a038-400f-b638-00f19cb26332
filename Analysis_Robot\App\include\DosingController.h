#ifndef DOSING_CONTROLLER_H
#define DOSING_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <string>
#include <mutex>
#include <map>
#include <nlohmann/json.hpp>


#include "PLCDriver.h"

/**
 * @brief 加液装置控制器类
 *
 * 实现加液装置的REST接口控制，包括：
 * - 蠕动泵控制 (通过PLC控制泵的启停和转速)
 * - 加液量控制
 * - 状态监控
 */
class DosingController : public AnalysisRobot::RestInterface::IDeviceController {
public:
    /**
     * @brief 构造函数
     * @param plcDriver PLC驱动实例
     */
    explicit DosingController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver = nullptr);

    /**
     * @brief 析构函数
     */
    ~DosingController();

    // 实现IDeviceController接口
    AnalysisRobot::RestInterface::DeviceStatus getStatus() override;
    AnalysisRobot::RestInterface::TaskInfo executeOperation(const nlohmann::json& request) override;
    AnalysisRobot::RestInterface::TaskInfo queryTask(int taskId) override;

private:
    std::shared_ptr<AnalysisRobot::PLC::PLCDriver> m_plcDriver;          // PLC驱动
    std::map<int, AnalysisRobot::RestInterface::TaskInfo> m_tasks;       // 任务存储
    std::mutex m_tasksMutex;                                             // 任务锁
    int m_nextTaskId;                                                    // 下一个任务ID

    // PLC寄存器地址定义
    static const int PUMP_CONTROL_ADDR = 400;       // 泵控制地址
    static const int PUMP_STATUS_ADDR = 410;        // 泵状态地址
    static const int VOLUME_CONTROL_ADDR = 420;     // 加液量控制地址
    static const int SPEED_CONTROL_ADDR = 430;      // 转速控制地址

    /**
     * @brief 处理开始加液操作
     * @param data 请求数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleStartDosingOperation(const nlohmann::json& data);

    /**
     * @brief 处理停止加液操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleStopDosingOperation();

    /**
     * @brief 处理设置转速操作
     * @param data 请求数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleSetSpeedOperation(const nlohmann::json& data);

    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();

    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSuccessTask(const std::string& action, const nlohmann::json& data = nlohmann::json());

    /**
     * @brief 创建错误任务
     * @param action 操作名称
     * @param error 错误信息
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createErrorTask(const std::string& action, const std::string& error);

    /**
     * @brief 创建提交任务
     * @param action 操作名称
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSubmittedTask(const std::string& action);

    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString() const;


};

#endif // DOSING_CONTROLLER_H
