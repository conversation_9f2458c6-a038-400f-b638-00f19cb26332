#ifndef FILTER_CONTROLLER_H
#define FILTER_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <string>
#include <mutex>
#include <map>
#include <nlohmann/json.hpp>


#include "PLCDriver.h"

/**
 * @brief 过滤装置控制器类
 * 
 * 实现过滤装置的REST接口控制，包括：
 * - 过滤操作 (通过PLC控制过滤装置)
 * - 压力监控
 * - 状态监控
 */
class FilterController : public AnalysisRobot::RestInterface::IDeviceController {
public:
    /**
     * @brief 构造函数
     * @param plcDriver PLC驱动实例
     */
    explicit FilterController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~FilterController();
    
    // 实现IDeviceController接口
    AnalysisRobot::RestInterface::DeviceStatus getStatus() override;
    AnalysisRobot::RestInterface::TaskInfo executeOperation(const nlohmann::json& request) override;
    AnalysisRobot::RestInterface::TaskInfo queryTask(int taskId) override;

private:
    std::shared_ptr<AnalysisRobot::PLC::PLCDriver> m_plcDriver;          // PLC驱动
    std::map<int, AnalysisRobot::RestInterface::TaskInfo> m_tasks;       // 任务存储
    std::mutex m_tasksMutex;                                             // 任务锁
    int m_nextTaskId;                                                    // 下一个任务ID
    
    // PLC寄存器地址定义
    static const int FILTER_CONTROL_ADDR = 600;     // 过滤控制地址
    static const int FILTER_STATUS_ADDR = 610;      // 过滤状态地址
    
    /**
     * @brief 处理开启过滤操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleOpenFilterOperation();

    /**
     * @brief 处理关闭过滤操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleCloseFilterOperation();
    
    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSuccessTask(const std::string& action, const nlohmann::json& data = nlohmann::json());
    
    /**
     * @brief 创建错误任务
     * @param action 操作名称
     * @param error 错误信息
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createErrorTask(const std::string& action, const std::string& error);
    
    /**
     * @brief 创建提交任务
     * @param action 操作名称
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSubmittedTask(const std::string& action);
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString() const;
    

};

#endif // FILTER_CONTROLLER_H
