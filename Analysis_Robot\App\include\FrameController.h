#ifndef FRAME_CONTROLLER_H
#define FRAME_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <string>
#include <mutex>
#include <map>
#include <nlohmann/json.hpp>


#include "PLCDriver.h"

/**
 * @brief 框架控制器类
 * 
 * 实现框架的REST接口控制，包括：
 * - 框架位置控制 (通过PLC控制框架移动)
 * - 状态监控
 */
class FrameController : public AnalysisRobot::RestInterface::IDeviceController {
public:
    /**
     * @brief 构造函数
     * @param plcDriver PLC驱动实例
     */
    explicit FrameController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~FrameController();
    
    // 实现IDeviceController接口
    AnalysisRobot::RestInterface::DeviceStatus getStatus() override;
    AnalysisRobot::RestInterface::TaskInfo executeOperation(const nlohmann::json& request) override;
    AnalysisRobot::RestInterface::TaskInfo queryTask(int taskId) override;

private:
    std::shared_ptr<AnalysisRobot::PLC::PLCDriver> m_plcDriver;          // PLC驱动
    std::map<int, AnalysisRobot::RestInterface::TaskInfo> m_tasks;       // 任务存储
    std::mutex m_tasksMutex;                                             // 任务锁
    int m_nextTaskId;                                                    // 下一个任务ID
    
    // PLC寄存器地址定义
    static const int FRAME_CONTROL_ADDR = 1000;     // 框架控制地址
    static const int FRAME_STATUS_ADDR = 1010;      // 框架状态地址
    static const int POSITION_ADDR = 1020;          // 位置地址
    
    /**
     * @brief 处理移动框架操作
     * @param data 请求数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleMoveFrameOperation(const nlohmann::json& data);
    
    /**
     * @brief 处理停止框架操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleStopFrameOperation();
    
    /**
     * @brief 处理获取位置操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleGetPositionOperation();
    
    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSuccessTask(const std::string& action, const nlohmann::json& data = nlohmann::json());
    
    /**
     * @brief 创建错误任务
     * @param action 操作名称
     * @param error 错误信息
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createErrorTask(const std::string& action, const std::string& error);
    
    /**
     * @brief 创建提交任务
     * @param action 操作名称
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSubmittedTask(const std::string& action);
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString() const;
    

};

#endif // FRAME_CONTROLLER_H
