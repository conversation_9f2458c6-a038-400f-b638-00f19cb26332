#ifndef ICP_ENTRY_CONTROLLER_H
#define ICP_ENTRY_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <string>
#include <mutex>
#include <map>
#include <nlohmann/json.hpp>


#include "PLCDriver.h"

/**
 * @brief ICP进样装置控制器类
 * 
 * 实现ICP进样装置的REST接口控制，包括：
 * - 进样泵控制 (通过PLC控制进样泵)
 * - 进样量控制
 * - 状态监控
 */
class ICPEntryController : public AnalysisRobot::RestInterface::IDeviceController {
public:
    /**
     * @brief 构造函数
     * @param plcDriver PLC驱动实例
     */
    explicit ICPEntryController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver = nullptr);
    
    /**
     * @brief 析构函数
     */
    ~ICPEntryController();
    
    // 实现IDeviceController接口
    AnalysisRobot::RestInterface::DeviceStatus getStatus() override;
    AnalysisRobot::RestInterface::TaskInfo executeOperation(const nlohmann::json& request) override;
    AnalysisRobot::RestInterface::TaskInfo queryTask(int taskId) override;

private:
    std::shared_ptr<AnalysisRobot::PLC::PLCDriver> m_plcDriver;          // PLC驱动
    std::map<int, AnalysisRobot::RestInterface::TaskInfo> m_tasks;       // 任务存储
    std::mutex m_tasksMutex;                                             // 任务锁
    int m_nextTaskId;                                                    // 下一个任务ID
    
    // PLC寄存器地址定义
    static const int ICP_PUMP_CONTROL_ADDR = 800;   // ICP泵控制地址
    static const int ICP_PUMP_STATUS_ADDR = 810;    // ICP泵状态地址
    static const int ICP_VOLUME_ADDR = 820;         // 进样量地址
    static const int ICP_SPEED_ADDR = 830;          // 进样速度地址
    
    /**
     * @brief 处理开始进样操作
     * @param data 请求数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleStartSamplingOperation(const nlohmann::json& data);
    
    /**
     * @brief 处理停止进样操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleStopSamplingOperation();
    
    /**
     * @brief 处理设置进样参数操作
     * @param data 请求数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleSetParametersOperation(const nlohmann::json& data);
    
    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSuccessTask(const std::string& action, const nlohmann::json& data = nlohmann::json());
    
    /**
     * @brief 创建错误任务
     * @param action 操作名称
     * @param error 错误信息
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createErrorTask(const std::string& action, const std::string& error);
    
    /**
     * @brief 创建提交任务
     * @param action 操作名称
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSubmittedTask(const std::string& action);
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString() const;
    

};

#endif // ICP_ENTRY_CONTROLLER_H
