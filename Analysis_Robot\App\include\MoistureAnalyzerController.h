﻿#ifndef MOISTURE_ANALYZER_CONTROLLER_H
#define MOISTURE_ANALYZER_CONTROLLER_H

#include "RestInterfaceDriver.h"
#include <memory>
#include <string>
#include <mutex>
#include <map>
#include <nlohmann/json.hpp>

#include "DeviceManager.h"
#include "../../drivers/moistureAnalyzerDriver/include/MoistureAnalyzerDriver.h"

/**
 * @brief 水分测定仪控制器类
 * 
 * 实现水分测定仪设备的REST接口控制，包括：
 * - 清零、去皮、开关盖、加热操作
 * - 称重操作
 * - 获取检测结果
 * - 状态查询
 */
class MoistureAnalyzerController : public AnalysisRobot::RestInterface::IDeviceController {
public:
    /**
     * @brief 构造函数
     * @param driver 水分测定仪驱动实例
     */
    explicit MoistureAnalyzerController(std::shared_ptr<AnalysisRobot::Moisture::MoistureAnalyzerDriver> driver);
    
    /**
     * @brief 析构函数
     */
    ~MoistureAnalyzerController();
    
    // 实现IDeviceController接口
    AnalysisRobot::RestInterface::DeviceStatus getStatus() override;
    AnalysisRobot::RestInterface::TaskInfo executeOperation(const nlohmann::json& request) override;
    AnalysisRobot::RestInterface::TaskInfo queryTask(int taskId) override;

private:
    std::shared_ptr<AnalysisRobot::Moisture::MoistureAnalyzerDriver> m_driver; // 水分测定仪驱动
    std::map<int, AnalysisRobot::RestInterface::TaskInfo> m_tasks;                     // 任务存储
    std::mutex m_tasksMutex;                                                           // 任务锁
    int m_nextTaskId;                                                                  // 下一个任务ID
    
    /**
     * @brief 处理清零操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleZeroOperation();
    
    /**
     * @brief 处理去皮操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleBareOperation();
    
    /**
     * @brief 处理开盖操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleOpenOperation();
    
    /**
     * @brief 处理关盖操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleCloseOperation();
    
    /**
     * @brief 处理加热操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleHeatOperation();
    
    /**
     * @brief 处理称重操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleWeightOperation();
    
    /**
     * @brief 处理获取结果操作
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo handleGetResultOperation();
    
    /**
     * @brief 生成任务ID
     * @return 新的任务ID
     */
    int generateTaskId();
    
    /**
     * @brief 创建成功任务
     * @param action 操作名称
     * @param data 数据
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createSuccessTask(const std::string& action, const nlohmann::json& data = nlohmann::json());
    
    /**
     * @brief 创建错误任务
     * @param action 操作名称
     * @param error 错误信息
     * @return 任务信息
     */
    AnalysisRobot::RestInterface::TaskInfo createErrorTask(const std::string& action, const std::string& error);
    
    /**
     * @brief 获取当前时间字符串
     * @return 时间字符串
     */
    std::string getCurrentTimeString() const;
    

};

#endif // MOISTURE_ANALYZER_CONTROLLER_H
