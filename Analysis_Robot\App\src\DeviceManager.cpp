#include "DeviceManager.h"
#include "BalanceController.h"
#include "HeatingMagneticStirrerController.h"
#include "MoistureAnalyzerController.h"
#include "RobotController.h"
#include "MoistureAnalyzerDriver.h"
#include "SampleEntryController.h"
#include "SampleExitController.h"
#include "RepoController.h"
#include "DosingController.h"
#include "VolumeController.h"
#include "FilterController.h"
#include "StirController.h"
#include "ICPEntryController.h"
#include "ShakerController.h"
#include "FrameController.h"
#include "PouringController.h"

// 硬件驱动头文件
#include "BalanceDriver.h"
#include "HeatingMagneticStirrerDriver.h"
#include "MoistureAnalyzerDriver.h"
#include "RobotDriver.h"

#include <iostream>
#include <sstream>
#include "glog.h"

DeviceManager::DeviceManager() {
    LOG(INFO) << "Device manager created";
}

DeviceManager::~DeviceManager() {
    stopAllDevices();
    disconnectAllDevices();
    LOG(INFO) << "Device manager destroyed";
}

bool DeviceManager::initialize(const DeviceConfig& devicesConfig) {
    LOG(INFO) << "Initializing device manager";
    LOG(INFO) << "Device config contains " << devicesConfig.balances.size() << " balances, "
              << devicesConfig.stirrer_heaters.size() << " stirrer heaters";

    try {
        // 初始化天平设备
        if (!devicesConfig.balances.empty()) {
            LOG(INFO) << "Found " << devicesConfig.balances.size() << " balances in config";
            if (!initializeBalances(devicesConfig.balances)) {
                LOG(ERROR) << "Failed to initialize balance devices: " << getLastError();
                setError("Failed to initialize balance devices");
                return false;
            }
        } else {
            LOG(WARNING) << "No balances found in config";
        }

        // 初始化搅拌加热设备
        if (!devicesConfig.stirrer_heaters.empty()) {
            LOG(INFO) << "Found " << devicesConfig.stirrer_heaters.size() << " stirrer heaters in config";
            if (!initializeStirrerHeaters(devicesConfig.stirrer_heaters)) {
                LOG(ERROR) << "Failed to initialize stirrer heater devices: " << getLastError();
                setError("Failed to initialize stirrer heater devices");
                return false;
            }
        } else {
            LOG(WARNING) << "No stirrer heaters found in config";
        }

        // 初始化水分测定仪
        if (devicesConfig.moisture_analyzer.enabled) {
            LOG(INFO) << "Found moisture analyzer in config";
            if (!initializeMoistureAnalyzer(devicesConfig.moisture_analyzer)) {
                LOG(ERROR) << "Failed to initialize moisture analyzer: " << getLastError();
                setError("Failed to initialize moisture analyzer");
                return false;
            }
        } else {
            LOG(WARNING) << "Moisture analyzer is disabled";
        }

        // 初始化机器人
        if (!devicesConfig.robots.empty()) {
            LOG(INFO) << "Found " << devicesConfig.robots.size() << " robots in config";
            if (!initializeRobots(devicesConfig.robots)) {
                LOG(ERROR) << "Failed to initialize robot devices: " << getLastError();
                LOG(WARNING) << "Continuing initialization without robot devices";
                // 不返回false，继续初始化其他设备
            }
        } else {
            LOG(WARNING) << "No robots found in config";
        }

        // 初始化其他设备
        if (!initializeOtherDevices(devicesConfig)) {
            setError("Failed to initialize other devices");
            return false;
        }
        
        LOG(INFO) << "Device manager initialized successfully";
        return true;

    } catch (const std::exception& e) {
        LOG(ERROR) << "Exception during device manager initialization: " << e.what();
        LOG(WARNING) << "Some devices may not be available, but continuing with available devices";
        // 不设置错误，让应用程序继续运行
        return true; // 改为返回true，让应用程序继续运行
    }
}

bool DeviceManager::startAllDevices() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    LOG(INFO) << "Starting all devices";

    // 这里可以添加设备启动逻辑
    // 目前大部分设备在连接时就会启动

    LOG(INFO) << "All devices started";
    return true;
}

void DeviceManager::stopAllDevices() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    LOG(INFO) << "Stopping all devices";

    // 停止所有设备控制器
    // 控制器的析构函数会处理清理工作

    LOG(INFO) << "All devices stopped";
}

bool DeviceManager::connectAllDevices() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    LOG(INFO) << "Checking and connecting devices that need connection";

    bool allConnected = true;
    int alreadyConnected = 0;
    int newConnections = 0;
    int failedConnections = 0;

    // 连接天平设备
    for (size_t i = 0; i < m_balanceDrivers.size(); ++i) {
        auto& driver = m_balanceDrivers[i];
        if (driver) {
            if (!driver->isConnected()) {
                LOG(INFO) << "Attempting to connect balance device " << i;
                if (!driver->connect()) {
                    LOG(ERROR) << "Failed to connect balance device " << i;
                    allConnected = false;
                    failedConnections++;
                } else {
                    LOG(INFO) << "Balance device " << i << " connected successfully";
                    newConnections++;
                }
            } else {
                LOG(INFO) << "Balance device " << i << " already connected";
                alreadyConnected++;
            }
        }
    }

    // 连接搅拌加热设备
    for (size_t i = 0; i < m_stirrerDrivers.size(); ++i) {
        auto& driver = m_stirrerDrivers[i];
        if (driver) {
            if (!driver->isConnected()) {
                LOG(INFO) << "Attempting to connect stirrer heater device " << i;
                if (!driver->connect()) {
                    LOG(ERROR) << "Failed to connect stirrer heater device " << i;
                    allConnected = false;
                    failedConnections++;
                } else {
                    LOG(INFO) << "Stirrer heater device " << i << " connected successfully";
                    newConnections++;
                }
            } else {
                LOG(INFO) << "Stirrer heater device " << i << " already connected";
                alreadyConnected++;
            }
        }
    }

    // 连接水分测定仪
    if (m_moistureDriver) {
        if (!m_moistureDriver->isConnected()) {
            LOG(INFO) << "Attempting to connect moisture analyzer";
            if (!m_moistureDriver->connect()) {
                LOG(ERROR) << "Failed to connect moisture analyzer";
                allConnected = false;
                failedConnections++;
            } else {
                LOG(INFO) << "Moisture analyzer connected successfully";
                newConnections++;
            }
        } else {
            LOG(INFO) << "Moisture analyzer already connected";
            alreadyConnected++;
        }
    }

    // 连接机器人设备
    for (size_t i = 0; i < m_robotDrivers.size(); ++i) {
        auto& driver = m_robotDrivers[i];
        if (driver) {
            if (!driver->isConnected()) {
                LOG(INFO) << "Attempting to connect robot device " << i;
                if (!driver->connect()) {
                    LOG(ERROR) << "Failed to connect robot device " << i;
                    allConnected = false;
                    failedConnections++;
                } else {
                    LOG(INFO) << "Robot device " << i << " connected successfully";
                    newConnections++;
                }
            } else {
                LOG(INFO) << "Robot device " << i << " already connected";
                alreadyConnected++;
            }
        }
    }

    LOG(INFO) << "Device connection summary: "
              << alreadyConnected << " already connected, "
              << newConnections << " newly connected, "
              << failedConnections << " failed";

    if (allConnected) {
        LOG(INFO) << "All devices are now connected";
    } else {
        LOG(WARNING) << "Some devices failed to connect";
    }

    return allConnected;
}

void DeviceManager::disconnectAllDevices() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    LOG(INFO) << "Disconnecting all devices";

    // 断开天平设备
    for (auto& driver : m_balanceDrivers) {
        if (driver) {
            driver->disconnect();
        }
    }

    // 断开搅拌加热设备
    for (auto& driver : m_stirrerDrivers) {
        if (driver) {
            driver->disconnect();
        }
    }

    // 断开水分测定仪
    if (m_moistureDriver) {
        m_moistureDriver->disconnect();
    }

    // 断开机器人设备
    for (auto& driver : m_robotDrivers) {
        if (driver) {
            driver->disconnect();
        }
    }

    LOG(INFO) << "All devices disconnected";
}

std::shared_ptr<AnalysisRobot::RestInterface::IDeviceController> 
DeviceManager::getDeviceController(const std::string& deviceName) {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    auto it = m_deviceControllers.find(deviceName);
    if (it != m_deviceControllers.end()) {
        return it->second;
    }
    
    return nullptr;
}

std::map<std::string, std::shared_ptr<AnalysisRobot::RestInterface::IDeviceController>> 
DeviceManager::getAllDeviceControllers() {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    return m_deviceControllers;
}

nlohmann::json DeviceManager::getDevicesStatus() const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);
    
    nlohmann::json status;
    status["total_devices"] = static_cast<int>(m_deviceControllers.size());
    status["connected_devices"] = 0;
    
    nlohmann::json devices(nlohmann::json::array());
    
    for (const auto& pair : m_deviceControllers) {
        nlohmann::json deviceStatus;
        deviceStatus["name"] = pair.first;
        
        try {
            auto status = pair.second->getStatus();
            deviceStatus["status"] = status.status;
            deviceStatus["message"] = status.message;
            deviceStatus["update_time"] = status.updateTime;
            
            if (status.status == "CONNECTED" || status.status == "IDLE") {
                deviceStatus["connected_devices"] = deviceStatus.value("connected_devices", 0) + 1;
            }
        } catch (const std::exception& e) {
            deviceStatus["status"] = "ERROR";
            deviceStatus["message"] = e.what();
        }

        devices.push_back(deviceStatus);
    }
    
    status["devices"] = devices;
    return status;
}

bool DeviceManager::areAllDevicesConnected() const {
    std::lock_guard<std::mutex> lock(m_devicesMutex);

    int connectedCount = 0;
    int totalCount = m_deviceControllers.size();

    for (const auto& pair : m_deviceControllers) {
        try {
            auto status = pair.second->getStatus();
            if (status.status == "CONNECTED" || status.status == "IDLE") {
                connectedCount++;
            } else {
                LOG(INFO) << "Device " << pair.first << " not connected, status: " << status.status;
            }
        } catch (const std::exception& e) {
            LOG(INFO) << "Failed to get status for device " << pair.first << ": " << e.what();
        }
    }

    bool allConnected = (connectedCount == totalCount);
    if (!allConnected) {
        LOG(INFO) << "Device connection status: " << connectedCount << "/" << totalCount << " connected";
    }

    return allConnected;
}

std::string DeviceManager::getLastError() const {
    return m_lastError;
}

bool DeviceManager::initializeBalances(const std::vector<DeviceConfig::BalanceDevice>& config) {
    LOG(INFO) << "Initializing balance devices";
    LOG(INFO) << "Balance config size: " << config.size();

    for (size_t i = 0; i < config.size(); ++i) {
        const DeviceConfig::BalanceDevice& balanceConfig = config[i];
        LOG(INFO) << "Processing balance " << i << ": " << balanceConfig.name;

        if (balanceConfig.name.empty() || balanceConfig.serial_port.empty()) {
            setError("Balance config missing required fields");
            LOG(ERROR) << "Balance config missing required fields for index " << i;
            return false;
        }

        if (!balanceConfig.enabled) {
            LOG(INFO) << "Balance " << balanceConfig.name << " is disabled, skipping";
            continue;
        }

        std::string name = balanceConfig.name;
        std::string serialPort = balanceConfig.serial_port;
        LOG(INFO) << "Initializing balance: " << name << " on port: " << serialPort;

        // 创建天平驱动
        auto driver = std::make_shared<AnalysisRobot::Balance::BalanceDriver>();

        AnalysisRobot::Balance::BalanceConfig driverConfig;
        driverConfig.serialPort = serialPort;
        driverConfig.baudRate = balanceConfig.baud_rate;
        driverConfig.slaveId = balanceConfig.slave_id;
        
        LOG(INFO) << "Attempting to initialize balance driver for: " << name;
        if (!driver->initialize(driverConfig)) {
            setError("Failed to initialize balance driver: " + name);
            LOG(ERROR) << "Failed to initialize balance driver: " << name;
            return false;
        }
        LOG(INFO) << "Balance driver initialized successfully: " << name;

        m_balanceDrivers.push_back(driver);

        // 创建天平控制器
        auto controller = std::make_shared<BalanceController>(driver);
        m_balanceControllers.push_back(controller);

        // 注册到设备控制器映射表
        registerDeviceController(name, controller);

        LOG(INFO) << "Balance device initialized: " << name;
    }
    
    return true;
}

bool DeviceManager::initializeStirrerHeaters(const std::vector<DeviceConfig::StirrerHeaterDevice>& config) {
    LOG(INFO) << "Initializing stirrer heater devices";
    LOG(INFO) << "Stirrer heater config size: " << config.size();

    for (size_t i = 0; i < config.size(); ++i) {
        const DeviceConfig::StirrerHeaterDevice& stirrerConfig = config[i];
        LOG(INFO) << "Processing stirrer heater " << i << ": " << stirrerConfig.name;

        if (stirrerConfig.name.empty() || stirrerConfig.serial_port.empty()) {
            setError("Stirrer heater config missing required fields");
            LOG(ERROR) << "Stirrer heater config missing required fields for index " << i;
            return false;
        }

        if (!stirrerConfig.enabled) {
            LOG(INFO) << "Stirrer heater " << stirrerConfig.name << " is disabled, skipping";
            continue;
        }

        std::string name = stirrerConfig.name;
        std::string serialPort = stirrerConfig.serial_port;
        LOG(INFO) << "Initializing stirrer heater: " << name << " on port: " << serialPort;

        // 创建搅拌加热驱动
        auto driver = std::make_shared<AnalysisRobot::HeatingMagneticStirrer::HeatingMagneticStirrerDriver>();

        AnalysisRobot::HeatingMagneticStirrer::StirrerConfig driverConfig;
        driverConfig.serialPort = serialPort;
        driverConfig.baudRate = stirrerConfig.baud_rate;
        driverConfig.slaveId = stirrerConfig.slave_id;

        LOG(INFO) << "Attempting to initialize stirrer heater driver for: " << name;
        if (!driver->initialize(driverConfig)) {
            setError("Failed to initialize stirrer heater driver: " + name);
            LOG(ERROR) << "Failed to initialize stirrer heater driver: " << name;
            return false;
        }
        LOG(INFO) << "Stirrer heater driver initialized successfully: " << name;

        m_stirrerDrivers.push_back(driver);

        // 创建搅拌加热控制器
        auto controller = std::make_shared<HeatingMagneticStirrerController>(driver);
        m_stirrerControllers.push_back(controller);

        // 注册到设备控制器映射表
        registerDeviceController(name, controller);

        LOG(INFO) << "Stirrer heater device initialized: " << name;
    }

    return true;
}

bool DeviceManager::initializeMoistureAnalyzer(const DeviceConfig::MoistureAnalyzerDevice& config) {
    LOG(INFO) << "Initializing moisture analyzer";

    if (config.name.empty() || config.serial_port.empty()) {
        setError("Moisture analyzer config missing required fields");
        return false;
    }

    std::string name = config.name;
    std::string serialPort = config.serial_port;

    // 创建水分测定仪驱动
    m_moistureDriver = std::make_shared<AnalysisRobot::Moisture::MoistureAnalyzerDriver>();

    AnalysisRobot::Moisture::MoistureConfig driverConfig;
    driverConfig.serialPort = serialPort;
    driverConfig.baudRate = config.baud_rate;
    driverConfig.slaveId = config.slave_id;

    if (!m_moistureDriver->initialize(driverConfig)) {
        setError("Failed to initialize moisture analyzer driver: " + name);
        return false;
    }

    // 创建水分测定仪控制器
    m_moistureController = std::make_shared<MoistureAnalyzerController>(m_moistureDriver);

    // 注册到设备控制器映射表
    registerDeviceController(name, m_moistureController);

    LOG(INFO) << "Moisture analyzer initialized: " << name;
    return true;
}

bool DeviceManager::initializeRobots(const std::vector<DeviceConfig::RobotDevice>& config) {
    LOG(INFO) << "Initializing robot devices";
    bool anySuccess = false;

    for (size_t i = 0; i < config.size(); ++i) {
        const DeviceConfig::RobotDevice& robotConfig = config[i];

        if (robotConfig.name.empty() || robotConfig.ip_address.empty()) {
            LOG(ERROR) << "Robot config missing required fields for index " << i;
            continue; // 跳过这个机器人，继续处理下一个
        }

        if (!robotConfig.enabled) {
            LOG(INFO) << "Robot " << robotConfig.name << " is disabled, skipping";
            continue;
        }

        std::string name = robotConfig.name;
        std::string ipAddress = robotConfig.ip_address;

        try {
            // 创建机器人驱动
            auto driver = std::make_shared<AnalysisRobot::Robot::RobotDriver>();

            AnalysisRobot::Robot::RobotConfig driverConfig;
            driverConfig.ipAddress = ipAddress;
            driverConfig.port = robotConfig.port;
            driverConfig.slaveId = robotConfig.slave_id;

            LOG(INFO) << "Attempting to initialize robot driver for: " << name << " at " << ipAddress << ":" << robotConfig.port;
            if (!driver->initialize(driverConfig)) {
                LOG(ERROR) << "Failed to initialize robot driver: " << name;
                continue; // 跳过这个机器人，继续处理下一个
            }
            LOG(INFO) << "Robot driver initialized successfully: " << name;

            m_robotDrivers.push_back(driver);

            // 创建机器人控制器
            auto controller = std::make_shared<RobotController>(driver);
            m_robotControllers.push_back(controller);

            // 注册到设备控制器映射表
            registerDeviceController(name, controller);

            LOG(INFO) << "Robot device initialized: " << name;
            anySuccess = true;
        } catch (const std::exception& e) {
            LOG(ERROR) << "Exception while initializing robot " << name << ": " << e.what();
            continue; // 跳过这个机器人，继续处理下一个
        }
    }

    // 如果至少有一个机器人初始化成功，或者没有机器人配置，都认为是成功的
    if (anySuccess || config.empty()) {
        LOG(INFO) << "Robot initialization completed. Successfully initialized "
                  << m_robotControllers.size() << " out of " << config.size() << " robots";
        return true;
    } else {
        LOG(WARNING) << "No robots were successfully initialized";
        return false; // 所有机器人都失败了
    }
}

bool DeviceManager::initializeOtherDevices(const DeviceConfig& config) {
    LOG(INFO) << "Initializing other devices";

    // 初始化进样传送控制器
    m_sampleEntryController = std::make_shared<SampleEntryController>();
    registerDeviceController("sampleEntry", m_sampleEntryController);
    LOG(INFO) << "Sample entry controller initialized";

    // 初始化出样传送控制器
    m_sampleExitController = std::make_shared<SampleExitController>();
    registerDeviceController("sampleExit", m_sampleExitController);
    LOG(INFO) << "Sample exit controller initialized";

    // 初始化容器货架控制器
    m_repoController = std::make_shared<RepoController>();
    registerDeviceController("repo", m_repoController);
    LOG(INFO) << "Repo controller initialized";

    // 初始化加液控制器
    m_dosingController = std::make_shared<DosingController>();
    registerDeviceController("dosing", m_dosingController);
    LOG(INFO) << "Dosing controller initialized";

    // 初始化定容控制器
    m_volumeController = std::make_shared<VolumeController>();
    registerDeviceController("volume", m_volumeController);
    LOG(INFO) << "Volume controller initialized";

    // 初始化过滤控制器
    m_filterController = std::make_shared<FilterController>();
    registerDeviceController("filter", m_filterController);
    LOG(INFO) << "Filter controller initialized";

    // 初始化搅拌控制器
    m_stirController = std::make_shared<StirController>();
    registerDeviceController("stir", m_stirController);
    LOG(INFO) << "Stir controller initialized";

    // 初始化ICP进样控制器
    m_icpEntryController = std::make_shared<ICPEntryController>();
    registerDeviceController("icpEntry", m_icpEntryController);
    LOG(INFO) << "ICP entry controller initialized";

    // 初始化摇床控制器
    m_shakerController = std::make_shared<ShakerController>();
    registerDeviceController("shaker", m_shakerController);
    LOG(INFO) << "Shaker controller initialized";

    // 初始化框架控制器
    m_frameController = std::make_shared<FrameController>();
    registerDeviceController("frame", m_frameController);
    LOG(INFO) << "Frame controller initialized";

    // 初始化倒样控制器 (需要天平和轴驱动)
    // 使用第一个天平驱动和第一个机器人的轴驱动
    std::shared_ptr<AnalysisRobot::Balance::BalanceDriver> balanceDriver = nullptr;
    std::shared_ptr<AnalysisRobot::Axis::AxisDriver> axisDriver = nullptr;

    if (!m_balanceDrivers.empty()) {
        balanceDriver = m_balanceDrivers[0];
    }

    if (!m_robotDrivers.empty()) {
        // 假设机器人驱动包含轴驱动接口，或者需要单独的轴驱动
        // 这里需要根据实际的机器人驱动实现来调整
        // axisDriver = m_robotDrivers[0]->getAxisDriver();
    }

    if (balanceDriver) {
        m_pouringController = std::make_shared<PouringDeviceController>(balanceDriver, axisDriver);
        registerDeviceController("pouring", m_pouringController);
        LOG(INFO) << "Pouring controller initialized";
    } else {
        LOG(WARNING) << "Pouring controller not initialized - no balance driver available";
    }

    return true;
}

void DeviceManager::registerDeviceController(const std::string& name,
                                           std::shared_ptr<AnalysisRobot::RestInterface::IDeviceController> controller) {
    LOG(INFO) << "Registering device controller: " << name;
    m_deviceControllers[name] = controller;
    LOG(INFO) << "Device controller registered successfully: " << name;
    LOG(INFO) << "Total registered controllers: " << m_deviceControllers.size();
}

void DeviceManager::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << error;
}
