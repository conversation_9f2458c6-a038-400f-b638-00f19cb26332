#include "DosingController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <thread>
#include "glog.h"

DosingController::DosingController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(8000) {
    LOG(INFO) << "Dosing controller created";
}

DosingController::~DosingController() {
    LOG(INFO) << "Dosing controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus DosingController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Dosing Device";
    status.description = "Peristaltic pump dosing system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }
    
    try {
        // 读取泵状态
        uint16_t pumpStatus = 0;
        if (m_plcDriver->readHoldingRegister(PUMP_STATUS_ADDR, pumpStatus)) {
            if (pumpStatus == 0) {
                status.status = "IDLE";
                status.message = "Device idle and available";
            } else if (pumpStatus == 1) {
                status.status = "BUSY";
                status.message = "Dosing in progress";
            } else {
                status.status = "FAILED";
                status.message = "Pump fault";
            }
            
            status.data["pump_status"] = pumpStatus;
        } else {
            status.status = "FAILED";
            status.message = "Cannot read device status";
        }
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "Status query exception: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo DosingController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "START") {
        return handleStartDosingOperation(request.value("data", nlohmann::json()));
    } else if (action == "STOP") {
        return handleStopDosingOperation();
    } else if (action == "SET_SPEED") {
        return handleSetSpeedOperation(request.value("data", nlohmann::json()));
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo DosingController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 检查任务状态是否需要更新
        if (it->second.status == AnalysisRobot::RestInterface::TaskStatus::SUBMITTED) {
            // 检查泵是否完成加液
            if (m_plcDriver && m_plcDriver->isConnected()) {
                uint16_t pumpStatus = 0;
                if (m_plcDriver->readHoldingRegister(PUMP_STATUS_ADDR, pumpStatus)) {
                    if (pumpStatus == 0) {
                        // 加液完成
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                        it->second.message = "Dosing completed";
                        it->second.updateTime = getCurrentTimeString();
                    } else if (pumpStatus > 1) {
                        // 加液失败
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                        it->second.message = "Dosing failed";
                        it->second.updateTime = getCurrentTimeString();
                    }
                }
            }
        }
        
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo DosingController::handleStartDosingOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("START", "PLC not connected");
        }
        
        // 设置加液量 (如果提供)
        if (data.contains("volume")) {
            int volume = data["volume"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(VOLUME_CONTROL_ADDR, static_cast<uint16_t>(volume))) {
                return createErrorTask("START", "Set dosing volume failed: " + m_plcDriver->getLastError());
            }
        }
        
        // 设置转速 (如果提供)
        if (data.contains("speed")) {
            int speed = data["speed"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(SPEED_CONTROL_ADDR, static_cast<uint16_t>(speed))) {
                return createErrorTask("START", "Set speed failed: " + m_plcDriver->getLastError());
            }
        }
        
        // 启动泵
        if (!m_plcDriver->writeHoldingRegister(PUMP_CONTROL_ADDR, 1)) {
            return createErrorTask("START", "Start pump failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSubmittedTask("START");
        nlohmann::json responseData;
        if (data.contains("volume")) {
            responseData["volume"] = data["volume"];
        }
        if (data.contains("speed")) {
            responseData["speed"] = data["speed"];
        }
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Dosing started";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("START", "Start dosing exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo DosingController::handleStopDosingOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("STOP", "PLC not connected");
        }
        
        // 停止泵
        if (!m_plcDriver->writeHoldingRegister(PUMP_CONTROL_ADDR, 0)) {
            return createErrorTask("STOP", "Stop pump failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSuccessTask("STOP");
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Dosing stopped";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop dosing exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo DosingController::handleSetSpeedOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("SET_SPEED", "PLC not connected");
        }
        
        if (!data.contains("speed")) {
            return createErrorTask("SET_SPEED", "Missing speed parameter");
        }
        
        int speed = data["speed"].get<int>();
        
        // 设置转速
        if (!m_plcDriver->writeHoldingRegister(SPEED_CONTROL_ADDR, static_cast<uint16_t>(speed))) {
            return createErrorTask("SET_SPEED", "Set speed failed: " + m_plcDriver->getLastError());
        }
        
        nlohmann::json responseData;
        responseData["speed"] = speed;
        
        auto task = createSuccessTask("SET_SPEED", responseData);
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Speed set to: " << speed;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("SET_SPEED", "Set speed exception: " + std::string(e.what()));
    }
}

int DosingController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo DosingController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo DosingController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo DosingController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "Task submitted successfully";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string DosingController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


