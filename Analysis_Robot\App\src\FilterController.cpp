#include "FilterController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <thread>
#include "glog.h"

FilterController::FilterController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(10000) {
    LOG(INFO) << "Filter controller created";
}

FilterController::~FilterController() {
    LOG(INFO) << "Filter controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus FilterController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Filter Device";
    status.description = "Filter valve control system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }
    
    try {
        // 读取过滤状态
        uint16_t filterStatus = 0;
        if (m_plcDriver->readHoldingRegister(FILTER_STATUS_ADDR, filterStatus)) {
            if (filterStatus == 0) {
                status.status = "CLOSED";
                status.message = "Filter valve closed";
            } else if (filterStatus == 1) {
                status.status = "OPEN";
                status.message = "Filter valve open";
            } else {
                status.status = "FAILED";
                status.message = "Filter device fault";
            }
            
            status.data["filter_status"] = filterStatus;
        } else {
            status.status = "FAILED";
            status.message = "Cannot read device status";
        }
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "Status query exception: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo FilterController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "OPEN") {
        return handleOpenFilterOperation();
    } else if (action == "CLOSE") {
        return handleCloseFilterOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo FilterController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo FilterController::handleOpenFilterOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("OPEN", "PLC not connected");
        }
        
        // 开启过滤阀门
        if (!m_plcDriver->writeHoldingRegister(FILTER_CONTROL_ADDR, 1)) {
            return createErrorTask("OPEN", "Open filter valve failed: " + m_plcDriver->getLastError());
        }
        
        // 等待阀门开启
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        // 检查阀门状态
        uint16_t filterStatus = 0;
        if (m_plcDriver->readHoldingRegister(FILTER_STATUS_ADDR, filterStatus)) {
            if (filterStatus == 1) {
                auto task = createSuccessTask("OPEN");
                task.message = "Filter valve opened successfully";
                
                // 存储任务
                {
                    std::lock_guard<std::mutex> lock(m_tasksMutex);
                    m_tasks[task.taskId] = task;
                }
                
                LOG(INFO) << "Filter opened successfully";
                return task;
            } else {
                return createErrorTask("OPEN", "Open filter valve failed, status code: " + std::to_string(filterStatus));
            }
        } else {
            return createErrorTask("OPEN", "Cannot read valve status");
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("OPEN", "Open filter valve exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo FilterController::handleCloseFilterOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("CLOSE", "PLC not connected");
        }
        
        // 关闭过滤阀门
        if (!m_plcDriver->writeHoldingRegister(FILTER_CONTROL_ADDR, 0)) {
            return createErrorTask("CLOSE", "Close filter valve failed: " + m_plcDriver->getLastError());
        }
        
        // 等待阀门关闭
        std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        
        // 检查阀门状态
        uint16_t filterStatus = 0;
        if (m_plcDriver->readHoldingRegister(FILTER_STATUS_ADDR, filterStatus)) {
            if (filterStatus == 0) {
                auto task = createSuccessTask("CLOSE");
                task.message = "Filter valve closed successfully";
                
                // 存储任务
                {
                    std::lock_guard<std::mutex> lock(m_tasksMutex);
                    m_tasks[task.taskId] = task;
                }
                
                LOG(INFO) << "Filter closed successfully";
                return task;
            } else {
                return createErrorTask("CLOSE", "Close filter valve failed, status code: " + std::to_string(filterStatus));
            }
        } else {
            return createErrorTask("CLOSE", "Cannot read valve status");
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("CLOSE", "Close filter valve exception: " + std::string(e.what()));
    }
}

int FilterController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo FilterController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo FilterController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo FilterController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "Task submitted successfully";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string FilterController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


