#include "FrameController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include "glog.h"

FrameController::FrameController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(14000) {
    LOG(INFO) << "Frame controller created";
}

FrameController::~FrameController() {
    LOG(INFO) << "Frame controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus FrameController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Frame";
    status.description = "Frame position control system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }
    
    try {
        // 读取框架状态
        uint16_t frameStatus = 0;
        if (m_plcDriver->readHoldingRegister(FRAME_STATUS_ADDR, frameStatus)) {
            if (frameStatus == 0) {
                status.status = "IDLE";
                status.message = "Frame stopped";
            } else if (frameStatus == 1) {
                status.status = "BUSY";
                status.message = "Frame moving";
            } else {
                status.status = "FAILED";
                status.message = "Frame device fault";
            }
            
            // 读取当前位置
            uint16_t position = 0;
            if (m_plcDriver->readHoldingRegister(POSITION_ADDR, position)) {
                status.data["position"] = position;
            }
            
            status.data["frame_status"] = frameStatus;
        } else {
            status.status = "FAILED";
            status.message = "Cannot read device status";
        }
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "Status query exception: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo FrameController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "MOVE") {
        return handleMoveFrameOperation(request.value("data", nlohmann::json()));
    } else if (action == "STOP") {
        return handleStopFrameOperation();
    } else if (action == "GET_POSITION") {
        return handleGetPositionOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo FrameController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 检查任务状态是否需要更新
        if (it->second.status == AnalysisRobot::RestInterface::TaskStatus::SUBMITTED) {
            // 检查框架是否完成移动
            if (m_plcDriver && m_plcDriver->isConnected()) {
                uint16_t frameStatus = 0;
                if (m_plcDriver->readHoldingRegister(FRAME_STATUS_ADDR, frameStatus)) {
                    if (frameStatus == 0) {
                        // 移动完成
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                        it->second.message = "Frame moved successfully";
                        it->second.updateTime = getCurrentTimeString();
                    } else if (frameStatus > 1) {
                        // 移动失败
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                        it->second.message = "Frame move failed";
                        it->second.updateTime = getCurrentTimeString();
                    }
                }
            }
        }
        
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task does not exist";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo FrameController::handleMoveFrameOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("MOVE", "PLC not connected");
        }
        
        if (!data.contains("position")) {
            return createErrorTask("MOVE", "Missing position parameter");
        }
        
        int targetPosition = data["position"].get<int>();
        
        // 设置目标位置
        if (!m_plcDriver->writeHoldingRegister(POSITION_ADDR, static_cast<uint16_t>(targetPosition))) {
            return createErrorTask("MOVE", "Set target position failed: " + m_plcDriver->getLastError());
        }
        
        // 启动框架移动
        if (!m_plcDriver->writeHoldingRegister(FRAME_CONTROL_ADDR, 1)) {
            return createErrorTask("MOVE", "Start frame move failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSubmittedTask("MOVE");
        nlohmann::json responseData;
        responseData["target_position"] = targetPosition;
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Frame move started to position: " << targetPosition;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("MOVE", "Frame move exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo FrameController::handleStopFrameOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("STOP", "PLC not connected");
        }
        
        // 停止框架移动
        if (!m_plcDriver->writeHoldingRegister(FRAME_CONTROL_ADDR, 0)) {
            return createErrorTask("STOP", "Stop frame move failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSuccessTask("STOP");
        task.message = "Frame stopped successfully";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Frame stopped";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop frame exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo FrameController::handleGetPositionOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("GET_POSITION", "PLC not connected");
        }
        
        // 读取当前位置
        uint16_t position = 0;
        if (!m_plcDriver->readHoldingRegister(POSITION_ADDR, position)) {
            return createErrorTask("GET_POSITION", "Read position failed: " + m_plcDriver->getLastError());
        }
        
        nlohmann::json responseData;
        responseData["position"] = position;
        
        auto task = createSuccessTask("GET_POSITION", responseData);
        task.message = "Get position successfully";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Current position: " << position;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("GET_POSITION", "Get position exception: " + std::string(e.what()));
    }
}

int FrameController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo FrameController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo FrameController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo FrameController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "Task submitted successfully";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string FrameController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


