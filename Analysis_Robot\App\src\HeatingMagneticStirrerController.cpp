#include "HeatingMagneticStirrerController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <nlohmann/json.hpp>
#include "glog.h"

using json = nlohmann::json;

HeatingMagneticStirrerController::HeatingMagneticStirrerController(
    std::shared_ptr<AnalysisRobot::HeatingMagneticStirrer::HeatingMagneticStirrerDriver> driver)
    : m_driver(driver)
    , m_nextTaskId(2000) {
    LOG(INFO) << "Heating magnetic stirrer controller created";
}

HeatingMagneticStirrerController::~HeatingMagneticStirrerController() {
    LOG(INFO) << "Heating magnetic stirrer controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus HeatingMagneticStirrerController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "加热磁力搅拌器";
    status.description = "智能加热磁力搅拌器";
    status.updateTime = getCurrentTimeString();
    
    if (!m_driver) {
        status.status = "ERROR";
        status.message = "驱动未初始化";
        return status;
    }
    
    if (!m_driver->isConnected()) {
        status.status = "DISCONNECTED";
        status.message = "Device not connected";
        return status;
    }
    
    auto driverStatus = m_driver->getStatus();
    switch (driverStatus) {
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::CONNECTED:
            status.status = "IDLE";
            status.message = "Device idle and available";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::HEATING:
            status.status = "HEATING";
            status.message = "正在加热";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::STIRRING:
            status.status = "STIRRING";
            status.message = "正在搅拌";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::HEATING_STIRRING:
            status.status = "WORKING";
            status.message = "正在加热搅拌";
            break;
        case AnalysisRobot::HeatingMagneticStirrer::StirrerStatus::FAULT:
            status.status = "FAILED";
            status.message = "Device fault: " + m_driver->getLastError();
            break;
        default:
            status.status = "UNKNOWN";
            status.message = "未知状态";
            break;
    }
    
    // 添加温度和转速信息
    try {
        auto reading = m_driver->readTemperatureAndSpeed();
        if (reading.success) {
            status.data["current_temperature"] = reading.temperature;
            status.data["current_speed"] = reading.speed;
        }
    } catch (const std::exception& e) {
        LOG(WARNING) << "Failed to read temperature and speed: " << e.what();
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::executeOperation(const json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }

    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "START") {
        json data = request.contains("data") ? request["data"] : json();
        return handleStartOperation(data);
    } else if (action == "STOP") {
        json data = request.contains("data") ? request["data"] : json();
        return handleStopOperation(data);
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::handleStartOperation(const json& data) {
    if (!m_driver) {
        return createErrorTask("START", "驱动未初始化");
    }

    if (!m_driver->isConnected()) {
        return createErrorTask("START", "Device not connected");
    }



    try {
        int channel = data.contains("channel") ? data["channel"].get<int>() : 0;

        // 根据接口规范，START操作默认使用智能快速升温功能
        // 默认参数：1200转、60°C、30分钟
        uint16_t speed = static_cast<uint16_t>(data.contains("speed") ? data["speed"].get<int>() : 1200);
        uint16_t temperature = static_cast<uint16_t>(data.contains("temperature") ? data["temperature"].get<int>() : 60);
        uint16_t timerMinutes = static_cast<uint16_t>(data.contains("timer_minutes") ? data["timer_minutes"].get<int>() : 30);

        LOG(INFO) << "Starting heating and stirring with smart heating: speed=" << speed
                  << " RPM, temperature=" << temperature << "°C, timer=" << timerMinutes << " min";

        // 使用智能快速升温接口
        if (m_driver->startStirringAndHeating(speed, temperature, timerMinutes)) {
            json responseData;
            responseData["channel"] = channel;
            responseData["heating"] = true;
            responseData["stirring"] = true;
            responseData["speed"] = speed;
            responseData["temperature"] = temperature;
            responseData["timer_minutes"] = timerMinutes;
            responseData["smart_heating"] = true;  // 标识使用了智能升温

            auto task = createSuccessTask("START", responseData);
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Smart heating and stirring started successfully for channel " << channel;
            return task;
        } else {
            return createErrorTask("START", "Smart heating start failed: " + m_driver->getLastError());
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("START", "Start exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::handleStopOperation(const json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("STOP", "Device not connected");
    }
    
    try {
        int channel = data.contains("channel") ? data["channel"].get<int>() : 0;

        LOG(INFO) << "Stopping heating and stirring for channel " << channel;

        // 根据接口规范，STOP操作执行：停止搅拌 → 冲洗动作 → 移盖操作
        // 使用智能停止接口一键停止所有功能
        if (m_driver->stopAll()) {
            json responseData;
            responseData["channel"] = channel;
            responseData["heating"] = false;
            responseData["stirring"] = false;
            responseData["timer_cleared"] = true;
            responseData["smart_stop"] = true;  // 标识使用了智能停止

            auto task = createSuccessTask("STOP", responseData);
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Smart stop operation completed for channel " << channel;
            return task;
        } else {
            return createErrorTask("STOP", "Smart stop failed: " + m_driver->getLastError());
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "停止异常: " + std::string(e.what()));
    }
}









int HeatingMagneticStirrerController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::createSuccessTask(const std::string& action, const json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo HeatingMagneticStirrerController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();

    return task;
}

std::string HeatingMagneticStirrerController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}




