﻿#include "ICPEntryController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include "glog.h"

ICPEntryController::ICPEntryController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(12000) {
    LOG(INFO) << "ICP entry controller created";
}

ICPEntryController::~ICPEntryController() {
    LOG(INFO) << "ICP entry controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus ICPEntryController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "ICP Sampling Device";
    status.description = "ICP sampling pump control system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }
    
    try {
        // 读取ICP泵状态
        uint16_t pumpStatus = 0;
        if (m_plcDriver->readHoldingRegister(ICP_PUMP_STATUS_ADDR, pumpStatus)) {
            if (pumpStatus == 0) {
                status.status = "IDLE";
                status.message = "ICP进样泵停止";
            } else if (pumpStatus == 1) {
                status.status = "BUSY";
                status.message = "ICP进样泵运行中";
            } else {
                status.status = "FAILED";
                status.message = "ICP进样泵故障";
            }
            
            status.data["pump_status"] = pumpStatus;
        } else {
            status.status = "FAILED";
            status.message = "无法读取设备状态";
        }
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "状态查询异常: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "START") {
        return handleStartSamplingOperation(request.value("data", nlohmann::json()));
    } else if (action == "STOP") {
        return handleStopSamplingOperation();
    } else if (action == "SET_PARAMETERS") {
        return handleSetParametersOperation(request.value("data", nlohmann::json()));
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 检查任务状态是否需要更新
        if (it->second.status == AnalysisRobot::RestInterface::TaskStatus::SUBMITTED) {
            // 检查泵是否完成进样
            if (m_plcDriver && m_plcDriver->isConnected()) {
                uint16_t pumpStatus = 0;
                if (m_plcDriver->readHoldingRegister(ICP_PUMP_STATUS_ADDR, pumpStatus)) {
                    if (pumpStatus == 0) {
                        // 进样完成
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                        it->second.message = "进样完成";
                        it->second.updateTime = getCurrentTimeString();
                    } else if (pumpStatus > 1) {
                        // 进样失败
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                        it->second.message = "进样失败";
                        it->second.updateTime = getCurrentTimeString();
                    }
                }
            }
        }
        
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::handleStartSamplingOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("START", "PLC not connected");
        }
        
        // 设置进样量 (如果提供)
        if (data.contains("volume")) {
            int volume = data["volume"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(ICP_VOLUME_ADDR, static_cast<uint16_t>(volume))) {
                return createErrorTask("START", "Set sampling volume failed: " + m_plcDriver->getLastError());
            }
        }
        
        // 设置进样速度 (如果提供)
        if (data.contains("speed")) {
            int speed = data["speed"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(ICP_SPEED_ADDR, static_cast<uint16_t>(speed))) {
                return createErrorTask("START", "Set sampling speed failed: " + m_plcDriver->getLastError());
            }
        }
        
        // 启动ICP进样泵
        if (!m_plcDriver->writeHoldingRegister(ICP_PUMP_CONTROL_ADDR, 1)) {
            return createErrorTask("START", "启动ICP进样泵失败: " + m_plcDriver->getLastError());
        }
        
        auto task = createSubmittedTask("START");
        nlohmann::json responseData;
        if (data.contains("volume")) {
            responseData["volume"] = data["volume"];
        }
        if (data.contains("speed")) {
            responseData["speed"] = data["speed"];
        }
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "ICP sampling started";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("START", "启动ICP进样异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::handleStopSamplingOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("STOP", "PLC not connected");
        }
        
        // 停止ICP进样泵
        if (!m_plcDriver->writeHoldingRegister(ICP_PUMP_CONTROL_ADDR, 0)) {
            return createErrorTask("STOP", "Stop ICP sampling pump failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSuccessTask("STOP");
        task.message = "ICP sampling stopped successfully";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "ICP sampling stopped";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop ICP sampling exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::handleSetParametersOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("SET_PARAMETERS", "PLC未连接");
        }
        
        nlohmann::json responseData;
        
        // 设置进样量
        if (data.contains("volume")) {
            int volume = data["volume"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(ICP_VOLUME_ADDR, static_cast<uint16_t>(volume))) {
                return createErrorTask("SET_PARAMETERS", "设置进样量失败: " + m_plcDriver->getLastError());
            }
            responseData["volume"] = volume;
        }
        
        // 设置进样速度
        if (data.contains("speed")) {
            int speed = data["speed"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(ICP_SPEED_ADDR, static_cast<uint16_t>(speed))) {
                return createErrorTask("SET_PARAMETERS", "设置进样速度失败: " + m_plcDriver->getLastError());
            }
            responseData["speed"] = speed;
        }
        
        auto task = createSuccessTask("SET_PARAMETERS", responseData);
        task.message = "ICP进样参数设置成功";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "ICP parameters set";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("SET_PARAMETERS", "设置ICP参数异常: " + std::string(e.what()));
    }
}

int ICPEntryController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "操作成功";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo ICPEntryController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "任务提交成功";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string ICPEntryController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


