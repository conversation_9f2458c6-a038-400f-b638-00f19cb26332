﻿#include "PouringController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <nlohmann/json.hpp>

#include "glog.h"


// 为了避免头文件名称冲突，我们需要重新设计这个类
// 暂时使用简化的实现，不依赖算法类

PouringDeviceController::PouringDeviceController(std::shared_ptr<AnalysisRobot::Balance::BalanceDriver> balanceDriver,
                                                std::shared_ptr<AnalysisRobot::Axis::AxisDriver> axisDriver)
    : m_balanceDriver(balanceDriver)
    , m_axisDriver(axisDriver)
    , m_nextTaskId(15000) {
    LOG(INFO) << "Pouring controller created";

    // 初始化倒样算法
    if (!initializePouringAlgorithm()) {
        LOG(ERROR) << "Failed to initialize pouring algorithm";
    }
}

PouringDeviceController::~PouringDeviceController() {
    // 确保停止任何正在进行的倒样操作
    if (m_pouringAlgorithm && m_pouringAlgorithm->isRunning()) {
        m_pouringAlgorithm->stopPouring();
    }
    LOG(INFO) << "Pouring controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus PouringDeviceController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Pouring Device";
    status.description = "Precision pouring control system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_balanceDriver || !m_axisDriver || !m_pouringAlgorithm) {
        status.status = "FAILED";
        status.message = "Device driver not initialized";
        return status;
    }

    if (!m_balanceDriver->isConnected() || !m_axisDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "Device not connected";
        return status;
    }
    
    try {
        // 获取倒样算法状态
        auto pouringState = m_pouringAlgorithm->getCurrentState();
        status.status = convertAlgorithmStatusToString(pouringState.status);
        status.message = pouringState.errorMessage;
        
        // 添加详细状态信息
        status.data["target_weight"] = pouringState.targetWeight;
        status.data["current_weight"] = pouringState.currentWeight;
        status.data["poured_weight"] = pouringState.pouredWeight;
        status.data["current_tilt_angle"] = pouringState.currentTiltAngle;
        status.data["elapsed_time"] = pouringState.elapsedTime;
        status.data["pid_output"] = pouringState.pidOutput;
        
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "Status query exception: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "START") {
        return handleStartPouringOperation(request.value("data", nlohmann::json()));
    } else if (action == "STOP") {
        return handleStopPouringOperation();
    } else if (action == "EMERGENCY_STOP") {
        return handleEmergencyStopOperation();
    } else if (action == "SET_PARAMETERS") {
        return handleSetParametersOperation(request.value("data", nlohmann::json()));
    } else if (action == "GET_STATUS") {
        return handleGetPouringStatusOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 检查任务状态是否需要更新
        if (it->second.status == AnalysisRobot::RestInterface::TaskStatus::SUBMITTED) {
            // 检查倒样是否完成
            if (m_pouringAlgorithm) {
                auto pouringState = m_pouringAlgorithm->getCurrentState();
                if (pouringState.status == AnalysisRobot::Algorithms::PouringStatus::COMPLETED) {
                    // 倒样完成
                    it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                    it->second.message = "倒样完成";
                    it->second.updateTime = getCurrentTimeString();
                    
                    // 添加结果数据
                    nlohmann::json resultData;
                    resultData["poured_weight"] = pouringState.pouredWeight;
                    resultData["target_weight"] = pouringState.targetWeight;
                    resultData["elapsed_time"] = pouringState.elapsedTime;
                    it->second.data = resultData;
                    
                } else if (pouringState.status == AnalysisRobot::Algorithms::PouringStatus::POURING_ERROR ||
                          pouringState.status == AnalysisRobot::Algorithms::PouringStatus::EMERGENCY_STOP) {
                    // 倒样失败
                    it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                    it->second.message = "倒样失败: " + pouringState.errorMessage;
                    it->second.updateTime = getCurrentTimeString();
                }
            }
        }
        
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::handleStartPouringOperation(const nlohmann::json& data) {
    try {
        if (!m_pouringAlgorithm) {
            return createErrorTask("START", "Pouring algorithm not initialized");
        }
        
        if (!data.contains("target_weight")) {
            return createErrorTask("START", "Missing target_weight parameter");
        }
        
        double targetWeight = data["target_weight"].get<double>();
        
        if (targetWeight <= 0) {
            return createErrorTask("START", "Invalid target weight: " + std::to_string(targetWeight));
        }
        
        // 检查是否已经在运行
        if (m_pouringAlgorithm->isRunning()) {
            return createErrorTask("START", "Pouring operation already in progress");
        }
        
        // 启动倒样
        if (!m_pouringAlgorithm->startPouring(targetWeight)) {
            return createErrorTask("START", "Start pouring failed");
        }
        
        auto task = createSubmittedTask("START");
        nlohmann::json responseData;
        responseData["target_weight"] = targetWeight;
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Pouring started with target weight: " << targetWeight << "g";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("START", "启动倒样异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::handleStopPouringOperation() {
    try {
        if (!m_pouringAlgorithm) {
            return createErrorTask("STOP", "Pouring algorithm not initialized");
        }
        
        m_pouringAlgorithm->stopPouring();
        
        auto task = createSuccessTask("STOP");
        task.message = "Pouring stopped successfully";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Pouring stopped";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop pouring exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::handleEmergencyStopOperation() {
    try {
        if (!m_pouringAlgorithm) {
            return createErrorTask("EMERGENCY_STOP", "倒样算法未初始化");
        }
        
        m_pouringAlgorithm->emergencyStop();
        
        auto task = createSuccessTask("EMERGENCY_STOP");
        task.message = "急停执行成功";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(WARNING) << "Emergency stop executed";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("EMERGENCY_STOP", "急停异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::handleSetParametersOperation(const nlohmann::json& data) {
    try {
        if (!m_pouringAlgorithm) {
            return createErrorTask("SET_PARAMETERS", "倒样算法未初始化");
        }

        nlohmann::json responseData;

        // 更新PID参数
        if (data.contains("pid_params")) {
            const nlohmann::json& pidData = data["pid_params"];
            AnalysisRobot::Algorithms::PIDParams pidParams;

            if (pidData.contains("kp")) pidParams.kp = pidData["kp"].get<double>();
            if (pidData.contains("ki")) pidParams.ki = pidData["ki"].get<double>();
            if (pidData.contains("kd")) pidParams.kd = pidData["kd"].get<double>();
            if (pidData.contains("max_output")) pidParams.maxOutput = pidData["max_output"].get<double>();
            if (pidData.contains("min_output")) pidParams.minOutput = pidData["min_output"].get<double>();
            if (pidData.contains("max_integral")) pidParams.maxIntegral = pidData["max_integral"].get<double>();

            m_pouringAlgorithm->updatePIDParams(pidParams);
            responseData["pid_params"] = pidData;
        }

        auto task = createSuccessTask("SET_PARAMETERS", responseData);
        task.message = "倒样参数设置成功";

        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }

        LOG(INFO) << "Pouring parameters updated";
        return task;

    } catch (const std::exception& e) {
        return createErrorTask("SET_PARAMETERS", "设置倒样参数异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::handleGetPouringStatusOperation() {
    try {
        if (!m_pouringAlgorithm) {
            return createErrorTask("GET_STATUS", "倒样算法未初始化");
        }

        auto pouringState = m_pouringAlgorithm->getCurrentState();

        nlohmann::json responseData;
        responseData["status"] = convertAlgorithmStatusToString(pouringState.status);
        responseData["message"] = pouringState.errorMessage;
        responseData["target_weight"] = pouringState.targetWeight;
        responseData["current_weight"] = pouringState.currentWeight;
        responseData["poured_weight"] = pouringState.pouredWeight;
        responseData["current_tilt_angle"] = pouringState.currentTiltAngle;
        responseData["elapsed_time"] = pouringState.elapsedTime;
        responseData["pid_output"] = pouringState.pidOutput;
        responseData["is_running"] = m_pouringAlgorithm->isRunning();

        auto task = createSuccessTask("GET_STATUS", responseData);

        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }

        return task;

    } catch (const std::exception& e) {
        return createErrorTask("GET_STATUS", "获取倒样状态异常: " + std::string(e.what()));
    }
}

bool PouringDeviceController::initializePouringAlgorithm() {
    try {
        if (!m_balanceDriver || !m_axisDriver) {
            LOG(ERROR) << "Balance or axis driver not provided";
            return false;
        }

        // 创建TCP位置保持算法
        auto tcpAlgorithm = std::make_shared<AnalysisRobot::Algorithms::TCPPositionMaintainAlgorithm>();

        // 创建坐标转换算法
        auto coordTransform = std::make_shared<AnalysisRobot::Algorithms::CoordinateTransform>();

        // 创建倒样算法控制器
        m_pouringAlgorithm = std::make_shared<AnalysisRobot::Algorithms::PouringControllerAlgorithms>(
            m_balanceDriver, m_axisDriver, tcpAlgorithm, coordTransform);

        // 初始化默认配置
        auto config = createDefaultConfig();
        if (!m_pouringAlgorithm->initialize(*config)) {
            LOG(ERROR) << "Failed to initialize pouring algorithm with config";
            return false;
        }

        LOG(INFO) << "Pouring algorithm initialized successfully";
        return true;

    } catch (const std::exception& e) {
        LOG(ERROR) << "Exception initializing pouring algorithm: " << e.what();
        return false;
    }
}

std::shared_ptr<AnalysisRobot::Algorithms::PouringConfig> PouringDeviceController::createDefaultConfig() {
    auto config = std::make_shared<AnalysisRobot::Algorithms::PouringConfig>();

    // 基本参数
    config->targetWeight = 100.0;          // 默认目标重量100g
    config->tolerance = 0.1;               // 允许误差0.1g
    config->maxPouringRate = 10.0;         // 最大倒样速率10g/s
    config->minPouringRate = 0.5;          // 最小倒样速率0.5g/s
    config->slowDownThreshold = 0.1;       // 减速阈值10%
    config->maxPouringTime = 60;           // 最大倒样时间60秒
    config->stabilizeTime = 1000;          // 稳定等待时间1秒

    // PID参数
    config->pidParams.kp = 2.0;
    config->pidParams.ki = 0.1;
    config->pidParams.kd = 0.05;
    config->pidParams.maxOutput = 100.0;
    config->pidParams.minOutput = -100.0;
    config->pidParams.maxIntegral = 50.0;

    // 轴控制参数
    config->tiltAxis = 3;                  // J3轴控制倾倒
    config->maxTiltAngle = 45.0;           // 最大倾倒角度45度
    config->minTiltAngle = 0.0;            // 最小倾倒角度0度
    config->initialTiltAngle = 5.0;        // 初始倾倒角度5度

    // TCP位置保持配置
    config->enableTCPMaintain = true;      // 启用TCP位置保持

    return config;
}

std::string PouringDeviceController::convertAlgorithmStatusToString(AnalysisRobot::Algorithms::PouringStatus status) {
    switch (status) {
        case AnalysisRobot::Algorithms::PouringStatus::IDLE:
            return "IDLE";
        case AnalysisRobot::Algorithms::PouringStatus::INITIALIZING:
            return "INITIALIZING";
        case AnalysisRobot::Algorithms::PouringStatus::TARING:
            return "TARING";
        case AnalysisRobot::Algorithms::PouringStatus::POURING:
            return "POURING";
        case AnalysisRobot::Algorithms::PouringStatus::COMPLETED:
            return "COMPLETED";
        case AnalysisRobot::Algorithms::PouringStatus::POURING_ERROR:
            return "FAILED";
        case AnalysisRobot::Algorithms::PouringStatus::EMERGENCY_STOP:
            return "EMERGENCY_STOP";
        default:
            return "UNKNOWN";
    }
}

int PouringDeviceController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "操作成功";
    task.data = data;
    task.updateTime = getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo PouringDeviceController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "任务提交成功";
    task.updateTime = getCurrentTimeString();

    return task;
}

std::string PouringDeviceController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


