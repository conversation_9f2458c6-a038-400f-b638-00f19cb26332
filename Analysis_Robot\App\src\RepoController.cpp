﻿#include "RepoController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <thread>
#include "glog.h"

RepoController::RepoController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(7000) {
    LOG(INFO) << "Repo controller created";
}

RepoController::~RepoController() {
    LOG(INFO) << "Repo controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus RepoController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Container Rack";
    status.description = "Automated container storage rack";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }
    
    try {
        // 读取电机状态
        uint16_t motorStatus = 0;
        if (m_plcDriver->readHoldingRegister(MOTOR_STATUS_ADDR, motorStatus)) {
            if (motorStatus == 0) {
                status.status = "IDLE";
                status.message = "Device idle and available";
            } else if (motorStatus == 1) {
                status.status = "BUSY";
                status.message = "Rack moving";
            } else {
                status.status = "FAILED";
                status.message = "Motor fault";
            }
            
            // 读取当前位置
            uint16_t currentPosition = 0;
            if (m_plcDriver->readHoldingRegister(POSITION_STATUS_ADDR, currentPosition)) {
                status.data["current_position"] = currentPosition;
            }
        } else {
            status.status = "FAILED";
            status.message = "Cannot read device status";
        }
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "Status query exception: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo RepoController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "MOVE_TO") {
        return handleMoveToPositionOperation(request.value("data", nlohmann::json()));
    } else if (action == "GET_POSITION") {
        return handleGetPositionOperation();
    } else if (action == "HOME") {
        return handleHomeOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo RepoController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 检查任务状态是否需要更新
        if (it->second.status == AnalysisRobot::RestInterface::TaskStatus::SUBMITTED) {
            // 检查电机是否完成移动
            if (m_plcDriver && m_plcDriver->isConnected()) {
                uint16_t motorStatus = 0;
                if (m_plcDriver->readHoldingRegister(MOTOR_STATUS_ADDR, motorStatus)) {
                    if (motorStatus == 0) {
                        // 移动完成
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                        it->second.message = "移动完成";
                        it->second.updateTime = getCurrentTimeString();
                    } else if (motorStatus > 1) {
                        // 移动失败
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                        it->second.message = "移动失败";
                        it->second.updateTime = getCurrentTimeString();
                    }
                }
            }
        }
        
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo RepoController::handleMoveToPositionOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("MOVE_TO", "PLC未连接");
        }
        
        if (!data.contains("position")) {
            return createErrorTask("MOVE_TO", "Missing position parameter");
        }
        
        int targetPosition = data["position"].get<int>();
        
        // 发送移动指令
        if (!m_plcDriver->writeHoldingRegister(POSITION_CONTROL_ADDR, static_cast<uint16_t>(targetPosition))) {
            return createErrorTask("MOVE_TO", "发送移动指令失败: " + m_plcDriver->getLastError());
        }
        
        auto task = createSubmittedTask("MOVE_TO");
        nlohmann::json responseData;
        responseData["target_position"] = targetPosition;
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Move to position task submitted: " << targetPosition;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("MOVE_TO", "移动异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo RepoController::handleGetPositionOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("GET_POSITION", "PLC未连接");
        }
        
        uint16_t currentPosition = 0;
        if (!m_plcDriver->readHoldingRegister(POSITION_STATUS_ADDR, currentPosition)) {
            return createErrorTask("GET_POSITION", "读取位置失败: " + m_plcDriver->getLastError());
        }
        
        nlohmann::json responseData;
        responseData["current_position"] = currentPosition;
        
        auto task = createSuccessTask("GET_POSITION", responseData);
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Current position: " << currentPosition;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("GET_POSITION", "获取位置异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo RepoController::handleHomeOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("HOME", "PLC未连接");
        }
        
        // 发送回原点指令 (假设0为原点位置)
        if (!m_plcDriver->writeHoldingRegister(POSITION_CONTROL_ADDR, 0)) {
            return createErrorTask("HOME", "发送回原点指令失败: " + m_plcDriver->getLastError());
        }
        
        auto task = createSubmittedTask("HOME");
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Home operation submitted";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("HOME", "回原点异常: " + std::string(e.what()));
    }
}

int RepoController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo RepoController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "操作成功";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo RepoController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo RepoController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "Task submitted successfully";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string RepoController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


