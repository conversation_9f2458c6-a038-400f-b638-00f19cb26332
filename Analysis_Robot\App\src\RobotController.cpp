#include "RobotController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include "glog.h"

RobotController::RobotController(std::shared_ptr<AnalysisRobot::Robot::RobotDriver> driver)
    : m_driver(driver)
    , m_nextTaskId(4000) {
    LOG(INFO) << "Robot controller created";
}

RobotController::~RobotController() {
    LOG(INFO) << "Robot controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus RobotController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Workbench Robot Arm";
    status.description = "Six-axis industrial robot arm";
    status.updateTime = getCurrentTimeString();
    
    if (!m_driver) {
        status.status = "FAILED";
        status.message = "Driver not initialized";
        return status;
    }

    if (!m_driver->isConnected()) {
        status.status = "FAILED";
        status.message = "Robot arm not connected";
        return status;
    }
    
    auto robotStatus = m_driver->getStatus();
    switch (robotStatus) {
        case AnalysisRobot::Robot::RobotStatus::IDLE:
            status.status = "IDLE";
            status.message = "Device idle and available";
            break;
        case AnalysisRobot::Robot::RobotStatus::BUSY:
            status.status = "BUSY";
            status.message = "Robot arm moving";
            break;
        case AnalysisRobot::Robot::RobotStatus::ROBOT_ERROR:
            status.status = "FAILED";
            status.message = "Robot arm error state";
            break;
        default:
            status.status = "UNKNOWN";
            status.message = "Unknown status";
            break;
    }
    
    // 添加当前位置信息
    try {
        auto position = m_driver->getCurrentPosition();
        status.data["x"] = position.x;
        status.data["y"] = position.y;
        status.data["z"] = position.z;
        status.data["u"] = position.rx;
        status.data["v"] = position.ry;
        status.data["w"] = position.rz;
    } catch (const std::exception& e) {
        LOG(WARNING) << "Failed to get current position: " << e.what();
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo RobotController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "PICK_PLACE") {
        return handlePickPlaceOperation(request.value("data", nlohmann::json()));
    } else if (action == "MOVE_TO") {
        return handleMoveToOperation(request.value("data", nlohmann::json()));
    } else if (action == "HOME") {
        return handleHomeOperation();
    } else if (action == "STOP") {
        return handleStopOperation();
    } else if (action == "SET_IO") {
        return handleSetIOOperation(request.value("data", nlohmann::json()));
    } else if (action == "GRIPPER") {
        return handleGripperOperation(request.value("data", nlohmann::json()));
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo RobotController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 检查任务状态是否需要更新
        if (it->second.status == AnalysisRobot::RestInterface::TaskStatus::SUBMITTED) {
            // 检查机器人是否完成任务
            if (m_driver && m_driver->isConnected()) {
                auto robotStatus = m_driver->getStatus();
                if (robotStatus == AnalysisRobot::Robot::RobotStatus::IDLE) {
                    // 任务完成
                    it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                    it->second.message = "Task completed";
                    it->second.updateTime = getCurrentTimeString();
                } else if (robotStatus == AnalysisRobot::Robot::RobotStatus::ROBOT_ERROR) {
                    // 任务失败
                    it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                    it->second.message = "Task execution failed";
                    it->second.updateTime = getCurrentTimeString();
                }
            }
        }
        
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "Task not found";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo RobotController::handlePickPlaceOperation(const nlohmann::json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("PICK_PLACE", "Robot not connected");
    }
    
    if (!data.contains("source") || !data.contains("destination")) {
        return createErrorTask("PICK_PLACE", "Missing source or destination parameter");
    }
    
    try {
        std::string source = data["source"].get<std::string>();
        std::string destination = data["destination"].get<std::string>();
        
        // 执行取放任务
        int taskId = m_driver->executePickPlace(source, destination);
        
        if (taskId > 0) {
            auto task = createSubmittedTask("PICK_PLACE");
            task.taskId = taskId; // 使用驱动返回的任务ID
            
            nlohmann::json responseData;
            responseData["source"] = source;
            responseData["destination"] = destination;
            task.data = responseData;
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Pick place task submitted: " << source << " -> " << destination;
            return task;
        } else {
            return createErrorTask("PICK_PLACE", "Pick place task submission failed");
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("PICK_PLACE", "Pick place exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo RobotController::handleMoveToOperation(const nlohmann::json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("MOVE_TO", "Robot not connected");
    }
    
    if (!data.contains("position")) {
        return createErrorTask("MOVE_TO", "Missing position parameter");
    }
    
    try {
        std::string positionCode = data["position"].get<std::string>();
        
        // 这里需要将位置编码转换为实际坐标
        // 暂时返回提交状态
        auto task = createSubmittedTask("MOVE_TO");
        
        nlohmann::json responseData;
        responseData["position"] = positionCode;
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Move to task submitted: " << positionCode;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("MOVE_TO", "Move exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo RobotController::handleHomeOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("HOME", "Robot not connected");
    }
    
    try {
        if (m_driver->home()) {
            auto task = createSubmittedTask("HOME");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Home operation submitted";
            return task;
        } else {
            return createErrorTask("HOME", "Home operation failed");
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("HOME", "Home exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo RobotController::handleStopOperation() {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("STOP", "Robot not connected");
    }
    
    try {
        if (m_driver->stop()) {
            auto task = createSuccessTask("STOP");
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Stop operation completed";
            return task;
        } else {
            return createErrorTask("STOP", "Stop operation failed");
        }

    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo RobotController::handleSetIOOperation(const nlohmann::json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("SET_IO", "Robot not connected");
    }
    
    if (!data.contains("port") || !data.contains("value")) {
        return createErrorTask("SET_IO", "Missing port or value parameter");
    }
    
    try {
        int port = data["port"].get<int>();
        bool value = data["value"].get<bool>();
        
        if (m_driver->setDigitalOutput(port, value)) {
            nlohmann::json responseData;
            responseData["port"] = port;
            responseData["value"] = value;
            
            auto task = createSuccessTask("SET_IO", responseData);
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "IO set: port " << port << " = " << (value ? "true" : "false");
            return task;
        } else {
            return createErrorTask("SET_IO", "IO set failed");
        }
        
    } catch (const std::exception& e) {
        return createErrorTask("SET_IO", "IO set failed: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo RobotController::handleGripperOperation(const nlohmann::json& data) {
    if (!m_driver || !m_driver->isConnected()) {
        return createErrorTask("GRIPPER", "Robot not connected");
    }
    
    if (!data.contains("command")) {
        return createErrorTask("GRIPPER", "Missing command parameter");
    }
    
    try {
        int command = data["command"];
        int force = data.value("force", 50);
        
        if (m_driver->controlGripper(command, force)) {
            nlohmann::json responseData;
            responseData["command"] = command;
            responseData["force"] = force;
            
            auto task = createSuccessTask("GRIPPER", responseData);
            
            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }
            
            LOG(INFO) << "Gripper command: " << command << ", force: " << force;
            return task;
        } else {
            return createErrorTask("GRIPPER", "Gripper control failed");
        }

    } catch (const std::exception& e) {
        return createErrorTask("GRIPPER", "Gripper control exception: " + std::string(e.what()));
    }
}

int RobotController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo RobotController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "操作成功";
    task.data = data;
    task.updateTime = getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo RobotController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();

    return task;
}

AnalysisRobot::RestInterface::TaskInfo RobotController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "Task submitted successfully";
    task.updateTime = getCurrentTimeString();

    return task;
}

std::string RobotController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);

    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


