﻿#include "SampleEntryController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <random>
#include <thread>
#include "glog.h"

SampleEntryController::SampleEntryController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(5000) {
    LOG(INFO) << "Sample entry controller created";
}

SampleEntryController::~SampleEntryController() {
    LOG(INFO) << "Sample entry controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus SampleEntryController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Sample Entry Device";
    status.description = "Automatic sample entry conveyor system";
    status.status = "IDLE";
    status.message = "Device idle and available";
    status.updateTime = getCurrentTimeString();
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "CHECK_SAMPLE") {
        return handleCheckSampleOperation();
    } else if (action == "SCAN_TAG") {
        return handleScanTagOperation();
    } else if (action == "OPEN_CAP") {
        return handleOpenCapOperation();
    } else if (action == "CLOSE_CAP") {
        return handleCloseCapOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::handleCheckSampleOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("CHECK_SAMPLE", "PLC未连接");
        }

        // 读取样品检测传感器状态
        uint16_t sensorValue = 0;
        if (!m_plcDriver->readHoldingRegister(SAMPLE_SENSOR_ADDR, sensorValue)) {
            return createErrorTask("CHECK_SAMPLE", "Read sensor failed: " + m_plcDriver->getLastError());
        }

        bool detected = (sensorValue > 0);

        if (detected) {
            auto task = createSuccessTask("CHECK_SAMPLE");
            task.message = "Scanning successful";

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Sample detected successfully, sensor value: " << sensorValue;
            return task;
        } else {
            auto task = createErrorTask("CHECK_SAMPLE", "未检测到样品来料");

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(WARNING) << "No sample detected, sensor value: " << sensorValue;
            return task;
        }

    } catch (const std::exception& e) {
        return createErrorTask("CHECK_SAMPLE", "检测异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::handleScanTagOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("SCAN_TAG", "PLC未连接");
        }

        // 启动扫码器旋转
        if (!m_plcDriver->writeHoldingRegister(SCANNER_CONTROL_ADDR, 1)) {
            return createErrorTask("SCAN_TAG", "Start scanner failed: " + m_plcDriver->getLastError());
        }

        // 等待扫码完成 (实际应用中可能需要轮询或中断)
        std::this_thread::sleep_for(std::chrono::milliseconds(2000));

        // 读取扫码结果
        uint16_t scanStatus = 0;
        if (!m_plcDriver->readHoldingRegister(SCANNER_CONTROL_ADDR, scanStatus)) {
            return createErrorTask("SCAN_TAG", "Read scan status failed: " + m_plcDriver->getLastError());
        }

        if (scanStatus == 2) { // 假设2表示扫码成功
            // 读取标签数据 (简化处理，实际可能需要读取多个寄存器)
            uint16_t tagData = 0;
            if (!m_plcDriver->readHoldingRegister(SCANNER_DATA_ADDR, tagData)) {
                return createErrorTask("SCAN_TAG", "Read tag data failed: " + m_plcDriver->getLastError());
            }

            std::ostringstream tagStream;
            tagStream << "TAG" << std::setfill('0') << std::setw(6) << tagData;
            std::string tag = tagStream.str();

            nlohmann::json responseData;
            responseData["tag"] = tag;

            auto task = createSuccessTask("SCAN_TAG", responseData);
            task.message = "Verification successful";

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Tag scanned successfully: " << tag;
            return task;
        } else {
            auto task = createErrorTask("SCAN_TAG", "Scan recognition failed, status code: " + std::to_string(scanStatus));

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(WARNING) << "Tag scan failed, status: " << scanStatus;
            return task;
        }

    } catch (const std::exception& e) {
        return createErrorTask("SCAN_TAG", "扫码异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::handleOpenCapOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("OPEN_CAP", "PLC未连接");
        }

        // 发送开盖指令
        if (!m_plcDriver->writeHoldingRegister(CAP_CONTROL_ADDR, 1)) {
            return createErrorTask("OPEN_CAP", "Send open cap command failed: " + m_plcDriver->getLastError());
        }

        // 等待开盖完成
        std::this_thread::sleep_for(std::chrono::milliseconds(3000));

        // 检查开盖状态
        uint16_t capStatus = 0;
        if (!m_plcDriver->readHoldingRegister(CAP_STATUS_ADDR, capStatus)) {
            return createErrorTask("OPEN_CAP", "Read open cap status failed: " + m_plcDriver->getLastError());
        }

        if (capStatus == 1) { // 假设1表示开盖成功
            auto task = createSuccessTask("OPEN_CAP");
            task.message = "Open cap successful";

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Cap opened successfully";
            return task;
        } else {
            return createErrorTask("OPEN_CAP", "Open cap failed, status code: " + std::to_string(capStatus));
        }

    } catch (const std::exception& e) {
        return createErrorTask("OPEN_CAP", "开盖异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::handleCloseCapOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("CLOSE_CAP", "PLC未连接");
        }

        // 发送关盖指令
        if (!m_plcDriver->writeHoldingRegister(CAP_CONTROL_ADDR, 2)) {
            return createErrorTask("CLOSE_CAP", "Send close cap command failed: " + m_plcDriver->getLastError());
        }

        // 等待关盖完成
        std::this_thread::sleep_for(std::chrono::milliseconds(3000));

        // 检查关盖状态
        uint16_t capStatus = 0;
        if (!m_plcDriver->readHoldingRegister(CAP_STATUS_ADDR, capStatus)) {
            return createErrorTask("CLOSE_CAP", "Read close cap status failed: " + m_plcDriver->getLastError());
        }

        if (capStatus == 2) { // 假设2表示关盖成功
            auto task = createSuccessTask("CLOSE_CAP");
            task.message = "Close cap successful";

            // 存储任务
            {
                std::lock_guard<std::mutex> lock(m_tasksMutex);
                m_tasks[task.taskId] = task;
            }

            LOG(INFO) << "Cap closed successfully";
            return task;
        } else {
            return createErrorTask("CLOSE_CAP", "Close cap failed, status code: " + std::to_string(capStatus));
        }

    } catch (const std::exception& e) {
        return createErrorTask("CLOSE_CAP", "关盖异常: " + std::string(e.what()));
    }
}

int SampleEntryController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo SampleEntryController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string SampleEntryController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


