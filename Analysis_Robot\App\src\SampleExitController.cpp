﻿#include "SampleExitController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <thread>
#include "glog.h"

SampleExitController::SampleExitController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(6000) {
    LOG(INFO) << "Sample exit controller created";
}

SampleExitController::~SampleExitController() {
    LOG(INFO) << "Sample exit controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus SampleExitController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Sample Exit Device";
    status.description = "Automatic sample exit conveyor system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }

    status.status = "IDLE";
    status.message = "Device idle and available";
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo SampleExitController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "CHECK_STATUS") {
        return handleCheckStatusOperation();
    } else if (action == "START_CONVEYOR") {
        return handleStartConveyorOperation();
    } else if (action == "STOP_CONVEYOR") {
        return handleStopConveyorOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo SampleExitController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo SampleExitController::handleCheckStatusOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("CHECK_STATUS", "PLC not connected");
        }
        
        std::string sensorStatus = readSensorStatus();
        
        auto task = createSuccessTask("CHECK_STATUS");
        task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
        task.message = "Signal detection successful";
        
        // 根据REST接口文档，返回状态应该在status字段中
        nlohmann::json responseData;
        responseData["status"] = sensorStatus;
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Conveyor sensor status: " << sensorStatus;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("CHECK_STATUS", "状态检测异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo SampleExitController::handleStartConveyorOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("START_CONVEYOR", "PLC未连接");
        }
        
        // 启动传送带
        if (!m_plcDriver->writeHoldingRegister(CONVEYOR_CONTROL_ADDR, 1)) {
            return createErrorTask("START_CONVEYOR", "启动传送带失败: " + m_plcDriver->getLastError());
        }
        
        auto task = createSuccessTask("START_CONVEYOR");
        task.message = "传送带启动成功";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Conveyor started successfully";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("START_CONVEYOR", "启动传送带异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo SampleExitController::handleStopConveyorOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("STOP_CONVEYOR", "PLC未连接");
        }
        
        // 停止传送带
        if (!m_plcDriver->writeHoldingRegister(CONVEYOR_CONTROL_ADDR, 0)) {
            return createErrorTask("STOP_CONVEYOR", "停止传送带失败: " + m_plcDriver->getLastError());
        }
        
        auto task = createSuccessTask("STOP_CONVEYOR");
        task.message = "传送带停止成功";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Conveyor stopped successfully";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP_CONVEYOR", "停止传送带异常: " + std::string(e.what()));
    }
}

std::string SampleExitController::readSensorStatus() {
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        return "FAILED";
    }
    
    try {
        uint16_t sensorValue = 0;
        if (!m_plcDriver->readHoldingRegister(CONVEYOR_SENSOR_ADDR, sensorValue)) {
            LOG(ERROR) << "Failed to read conveyor sensor: " << m_plcDriver->getLastError();
            return "FAILED";
        }
        
        // 根据传感器值判断状态
        if (sensorValue > 0) {
            return "FULL";  // 检测到物体，被占用
        } else {
            return "IDLE";  // 未检测到物体，空闲
        }
        
    } catch (const std::exception& e) {
        LOG(ERROR) << "Exception reading sensor status: " << e.what();
        return "FAILED";
    }
}

int SampleExitController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo SampleExitController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "操作成功";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo SampleExitController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string SampleExitController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


