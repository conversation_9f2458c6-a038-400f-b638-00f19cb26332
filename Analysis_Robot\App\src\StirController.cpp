﻿#include "StirController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include "glog.h"

StirController::StirController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(11000) {
    LOG(INFO) << "Stir controller created";
}

StirController::~StirController() {
    LOG(INFO) << "Stir controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus StirController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Stirring Device";
    status.description = "Stirring motor control system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }
    
    try {
        // 读取搅拌状态
        uint16_t stirStatus = 0;
        if (m_plcDriver->readHoldingRegister(STIR_STATUS_ADDR, stirStatus)) {
            if (stirStatus == 0) {
                status.status = "IDLE";
                status.message = "搅拌电机停止";
            } else if (stirStatus == 1) {
                status.status = "BUSY";
                status.message = "搅拌电机运行中";
            } else {
                status.status = "FAILED";
                status.message = "搅拌电机故障";
            }
            
            // 读取当前转速
            uint16_t currentSpeed = 0;
            if (m_plcDriver->readHoldingRegister(SPEED_STATUS_ADDR, currentSpeed)) {
                status.data["current_speed"] = currentSpeed;
            }
            
            status.data["stir_status"] = stirStatus;
        } else {
            status.status = "FAILED";
            status.message = "无法读取设备状态";
        }
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "状态查询异常: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo StirController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "START") {
        return handleStartStirOperation(request.value("data", nlohmann::json()));
    } else if (action == "STOP") {
        return handleStopStirOperation();
    } else if (action == "SET_SPEED") {
        return handleSetSpeedOperation(request.value("data", nlohmann::json()));
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo StirController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo StirController::handleStartStirOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("START", "PLC not connected");
        }
        
        // 设置转速 (如果提供)
        if (data.contains("speed")) {
            int speed = data["speed"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(SPEED_CONTROL_ADDR, static_cast<uint16_t>(speed))) {
                return createErrorTask("START", "Set speed failed: " + m_plcDriver->getLastError());
            }
        }
        
        // 启动搅拌
        if (!m_plcDriver->writeHoldingRegister(STIR_CONTROL_ADDR, 1)) {
            return createErrorTask("START", "Start stirring failed: " + m_plcDriver->getLastError());
        }
        
        nlohmann::json responseData;
        if (data.contains("speed")) {
            responseData["speed"] = data["speed"];
        }
        
        auto task = createSuccessTask("START", responseData);
        task.message = "搅拌启动成功";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Stirring started";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("START", "启动搅拌异常: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo StirController::handleStopStirOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("STOP", "PLC not connected");
        }
        
        // 停止搅拌
        if (!m_plcDriver->writeHoldingRegister(STIR_CONTROL_ADDR, 0)) {
            return createErrorTask("STOP", "Stop stirring failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSuccessTask("STOP");
        task.message = "Stirring stopped successfully";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Stirring stopped";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop stirring exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo StirController::handleSetSpeedOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("SET_SPEED", "PLC未连接");
        }
        
        if (!data.contains("speed")) {
            return createErrorTask("SET_SPEED", "Missing speed parameter");
        }
        
        int speed = data["speed"].get<int>();
        
        // 设置转速
        if (!m_plcDriver->writeHoldingRegister(SPEED_CONTROL_ADDR, static_cast<uint16_t>(speed))) {
            return createErrorTask("SET_SPEED", "设置转速失败: " + m_plcDriver->getLastError());
        }
        
        nlohmann::json responseData;
        responseData["speed"] = speed;
        
        auto task = createSuccessTask("SET_SPEED", responseData);
        task.message = "转速设置成功";
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Speed set to: " << speed;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("SET_SPEED", "设置转速异常: " + std::string(e.what()));
    }
}

int StirController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo StirController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "操作成功";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo StirController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string StirController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


