﻿#include "VolumeController.h"
#include <iostream>
#include <sstream>
#include <chrono>
#include <iomanip>
#include <thread>
#include "glog.h"

VolumeController::VolumeController(std::shared_ptr<AnalysisRobot::PLC::PLCDriver> plcDriver)
    : m_plcDriver(plcDriver)
    , m_nextTaskId(9000) {
    LOG(INFO) << "Volume controller created";
}

VolumeController::~VolumeController() {
    LOG(INFO) << "Volume controller destroyed";
}

AnalysisRobot::RestInterface::DeviceStatus VolumeController::getStatus() {
    AnalysisRobot::RestInterface::DeviceStatus status;
    
    status.name = "Volume Control Device";
    status.description = "Automatic volume control system";
    status.updateTime = getCurrentTimeString();
    
    if (!m_plcDriver || !m_plcDriver->isConnected()) {
        status.status = "FAILED";
        status.message = "PLC not connected";
        return status;
    }
    
    try {
        // 读取定容状态
        uint16_t volumeStatus = 0;
        if (m_plcDriver->readHoldingRegister(VOLUME_STATUS_ADDR, volumeStatus)) {
            if (volumeStatus == 0) {
                status.status = "IDLE";
                status.message = "Device idle and available";
            } else if (volumeStatus == 1) {
                status.status = "BUSY";
                status.message = "Volume control in progress";
            } else {
                status.status = "FAILED";
                status.message = "Volume control device fault";
            }
            
            // 读取液位信息
            uint16_t levelValue = 0;
            if (m_plcDriver->readHoldingRegister(LEVEL_SENSOR_ADDR, levelValue)) {
                status.data["level"] = levelValue;
            }
            
            status.data["volume_status"] = volumeStatus;
        } else {
            status.status = "FAILED";
            status.message = "Cannot read device status";
        }
    } catch (const std::exception& e) {
        status.status = "FAILED";
        status.message = "Status query exception: " + std::string(e.what());
    }
    
    return status;
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("UNKNOWN", "Missing action parameter");
    }
    
    std::string action = request["action"].get<std::string>();
    LOG(INFO) << "Executing operation: " << action;
    
    if (action == "START") {
        return handleStartVolumeOperation(request.value("data", nlohmann::json()));
    } else if (action == "STOP") {
        return handleStopVolumeOperation();
    } else if (action == "CHECK_LEVEL") {
        return handleCheckLevelOperation();
    } else {
        return createErrorTask(action, "Unsupported operation: " + action);
    }
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        // 检查任务状态是否需要更新
        if (it->second.status == AnalysisRobot::RestInterface::TaskStatus::SUBMITTED) {
            // 检查定容是否完成
            if (m_plcDriver && m_plcDriver->isConnected()) {
                uint16_t volumeStatus = 0;
                if (m_plcDriver->readHoldingRegister(VOLUME_STATUS_ADDR, volumeStatus)) {
                    if (volumeStatus == 0) {
                        // 定容完成
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
                        it->second.message = "定容完成";
                        it->second.updateTime = getCurrentTimeString();
                    } else if (volumeStatus > 1) {
                        // Volume control failed
                        it->second.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
                        it->second.message = "Volume control failed";
                        it->second.updateTime = getCurrentTimeString();
                    }
                }
            }
        }
        
        return it->second;
    }
    
    // 任务不存在
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = taskId;
    task.action = "QUERY";
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = "任务不存在";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::handleStartVolumeOperation(const nlohmann::json& data) {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("START", "PLC not connected");
        }
        
        // 设置目标容量 (如果提供)
        if (data.contains("target_volume")) {
            int targetVolume = data["target_volume"].get<int>();
            if (!m_plcDriver->writeHoldingRegister(TARGET_VOLUME_ADDR, static_cast<uint16_t>(targetVolume))) {
                return createErrorTask("START", "Set target volume failed: " + m_plcDriver->getLastError());
            }
        }
        
        // 启动定容
        if (!m_plcDriver->writeHoldingRegister(VOLUME_CONTROL_ADDR, 1)) {
            return createErrorTask("START", "Start volume control failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSubmittedTask("START");
        nlohmann::json responseData;
        if (data.contains("target_volume")) {
            responseData["target_volume"] = data["target_volume"];
        }
        task.data = responseData;
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Volume operation started";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("START", "Start volume control exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::handleStopVolumeOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("STOP", "PLC not connected");
        }
        
        // 停止定容
        if (!m_plcDriver->writeHoldingRegister(VOLUME_CONTROL_ADDR, 0)) {
            return createErrorTask("STOP", "Stop volume control failed: " + m_plcDriver->getLastError());
        }
        
        auto task = createSuccessTask("STOP");
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Volume operation stopped";
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("STOP", "Stop volume control exception: " + std::string(e.what()));
    }
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::handleCheckLevelOperation() {
    try {
        if (!m_plcDriver || !m_plcDriver->isConnected()) {
            return createErrorTask("CHECK_LEVEL", "PLC not connected");
        }
        
        // 读取液位
        uint16_t levelValue = 0;
        if (!m_plcDriver->readHoldingRegister(LEVEL_SENSOR_ADDR, levelValue)) {
            return createErrorTask("CHECK_LEVEL", "Read liquid level failed: " + m_plcDriver->getLastError());
        }
        
        nlohmann::json responseData;
        responseData["level"] = levelValue;
        
        auto task = createSuccessTask("CHECK_LEVEL", responseData);
        
        // 存储任务
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }
        
        LOG(INFO) << "Level checked: " << levelValue;
        return task;
        
    } catch (const std::exception& e) {
        return createErrorTask("CHECK_LEVEL", "Check liquid level exception: " + std::string(e.what()));
    }
}

int VolumeController::generateTaskId() {
    return m_nextTaskId++;
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUCCESS;
    task.message = "Operation successful";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::createErrorTask(const std::string& action, const std::string& error) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    
    return task;
}

AnalysisRobot::RestInterface::TaskInfo VolumeController::createSubmittedTask(const std::string& action) {
    AnalysisRobot::RestInterface::TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = AnalysisRobot::RestInterface::TaskStatus::SUBMITTED;
    task.message = "Task submitted successfully";
    task.updateTime = getCurrentTimeString();
    
    return task;
}

std::string VolumeController::getCurrentTimeString() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::ostringstream oss;
    oss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return oss.str();
}


