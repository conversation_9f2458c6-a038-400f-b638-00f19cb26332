﻿#include "RestInterfaceDriver.h"
#include "glog.h"
#include <sstream>
#include <chrono>
#include <iomanip>
#include <algorithm>
#include <ctime>

using json = nlohmann::json;

namespace AnalysisRobot {
namespace RestInterface {

RestInterfaceDriver::RestInterfaceDriver()
    : m_running(false)
    , m_nextTaskId(1000) {
}

RestInterfaceDriver::~RestInterfaceDriver() {
    stop();
}

bool RestInterfaceDriver::initialize(const RestConfig& config) {
    LOG(INFO) << "Initializing REST interface driver";
    LOG(INFO) << "Config - Host: " << config.host << ", Port: " << config.port;
    LOG(INFO) << "Config - Max connections: " << config.maxConnections;
    LOG(INFO) << "Config - Thread pool size: " << config.threadPoolSize;

    m_config = config;

    // 创建HTTP服务器
    LOG(INFO) << "Creating HTTP server";
    m_server = std::make_unique<httplib::Server>();

    if (!m_server) {
        LOG(ERROR) << "Failed to create HTTP server";
        return false;
    }

    // 初始化默认路由
    LOG(INFO) << "Initializing default routes";
    initializeDefaultRoutes();

    LOG(INFO) << "REST interface driver initialized successfully";
    return true;
}

bool RestInterfaceDriver::start() {
    if (m_running) {
        setError("Server is already running");
        return false;
    }
    
    if (!m_server) {
        setError("Server not initialized");
        return false;
    }
    
    m_running = true;
    m_serverThread = std::make_unique<std::thread>(&RestInterfaceDriver::serverLoop, this);
    
    LOG(INFO) << "REST server started on " << m_config.host << ":" << m_config.port;
    return true;
}

void RestInterfaceDriver::stop() {
    if (m_running) {
        m_running = false;
        if (m_server) {
            m_server->stop();
        }
        if (m_serverThread && m_serverThread->joinable()) {
            m_serverThread->join();
        }
        LOG(INFO) << "REST server stopped";
    }
}

bool RestInterfaceDriver::isRunning() const {
    return m_running;
}

void RestInterfaceDriver::registerDeviceController(const std::string& deviceName,
                                                  std::shared_ptr<IDeviceController> controller) {
    LOG(INFO) << "Registering device controller: " << deviceName;
    m_deviceControllers[deviceName] = controller;
    LOG(INFO) << "Device controller registered successfully: " << deviceName;
    LOG(INFO) << "Total registered devices: " << m_deviceControllers.size();
}

void RestInterfaceDriver::registerGetRoute(const std::string& path, RequestHandler handler) {
    if (m_server) {
        m_server->Get(path, handler);
        LOG(INFO) << "GET route registered: " << path;
    }
}

void RestInterfaceDriver::registerPostRoute(const std::string& path, RequestHandler handler) {
    if (m_server) {
        m_server->Post(path, handler);
        LOG(INFO) << "POST route registered: " << path;
    }
}

std::string RestInterfaceDriver::getServerInfo() const {
    std::ostringstream oss;
    oss << "REST Interface Driver\n";
    oss << "Host: " << m_config.host << "\n";
    oss << "Port: " << m_config.port << "\n";
    oss << "Status: " << (m_running ? "Running" : "Stopped") << "\n";
    oss << "Registered Devices: " << m_deviceControllers.size() << "\n";
    return oss.str();
}

std::string RestInterfaceDriver::getLastError() const {
    return m_lastError;
}

void RestInterfaceDriver::serverLoop() {
    LOG(INFO) << "Server loop started";
    LOG(INFO) << "Starting HTTP server on " << m_config.host << ":" << m_config.port;

    if (m_server) {
        LOG(INFO) << "Server object exists, calling listen()";
        // 启动HTTP服务器，这是阻塞调用
        bool result = m_server->listen(m_config.host, m_config.port);
        if (!result) {
            LOG(ERROR) << "Failed to start HTTP server on " << m_config.host << ":" << m_config.port;
            setError("Failed to start HTTP server on " + m_config.host + ":" + std::to_string(m_config.port));
        } else {
            LOG(INFO) << "HTTP server started successfully";
        }
    } else {
        LOG(ERROR) << "Server object is null!";
    }

    LOG(INFO) << "Server loop ended";
}

void RestInterfaceDriver::initializeDefaultRoutes() {
    LOG(INFO) << "Initializing default routes";
    if (!m_server) {
        LOG(ERROR) << "Server is null, cannot initialize routes";
        return;
    }
    
    // 健康检查端点
    m_server->Get("/health", [this](const httplib::Request& req, httplib::Response& res) {
        LOG(INFO) << "Received health check request from " << req.remote_addr;
        try {
            json response;
            response["status"] = "healthy";
            std::string timestamp = getCurrentTimeString();
            response["timestamp"] = timestamp;
            LOG(INFO) << "Health check response prepared with timestamp: " << timestamp;
            createJsonResponse(res, response);
            LOG(INFO) << "Health check response sent successfully";
        } catch (const std::exception& e) {
            LOG(ERROR) << "Health check error: " << e.what();
            res.status = 500;
            res.set_content("Internal Server Error: " + std::string(e.what()), "text/plain");
        } catch (...) {
            LOG(ERROR) << "Health check unknown error";
            res.status = 500;
            res.set_content("Internal Server Error: Unknown exception", "text/plain");
        }
    });
    
    // 服务器信息端点
    m_server->Get("/info", [this](const httplib::Request& req, httplib::Response& res) {
        json response;
        response["server"] = "Analysis Robot REST Interface";
        response["version"] = "1.0.0";
        response["timestamp"] = getCurrentTimeString();
        createJsonResponse(res, response);
    });
    
    // 注册设备API路由
    LOG(INFO) << "Setting up device routes";
    setupDeviceRoutes();
    LOG(INFO) << "Default routes initialized successfully";
}

void RestInterfaceDriver::setupDeviceRoutes() {
    LOG(INFO) << "Setting up device routes";
    if (!m_server) {
        LOG(ERROR) << "Server is null, cannot setup device routes";
        return;
    }
    
    // 设备状态查询路由
    m_server->Get(R"(/api/(\w+)/status)", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    // 设备操作路由
    m_server->Post(R"(/api/(\w+)/operation)", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 任务查询路由
    m_server->Get(R"(/api/(\w+)/query)", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 具体设备路由
    setupSpecificDeviceRoutes();
}

void RestInterfaceDriver::setupSpecificDeviceRoutes() {
    if (!m_server) return;
    
    // 进样传送装置
    m_server->Get("/api/sampleEntry/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    m_server->Post("/api/sampleEntry/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 出样传送装置
    m_server->Get("/api/sampleExit/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 容器货架装置
    m_server->Get("/api/repo/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/repo/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 机械臂
    m_server->Get("/api/robot/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/robot/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/robot/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 称量装置
    m_server->Get("/api/balance/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/balance/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/balance/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 倒样称量装置
    m_server->Get("/api/sampleBalance/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/sampleBalance/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/sampleBalance/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 加液装置
    m_server->Post("/api/dosing/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/dosing/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 定容装置
    m_server->Post("/api/volume/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/volume/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 过滤装置
    m_server->Post("/api/filter/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/filter/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 搅拌装置
    m_server->Post("/api/stir/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 搅拌加热装置
    m_server->Get("/api/heater/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/heater/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // ICP进样装置
    m_server->Post("/api/icpEntry/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/icpEntry/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 水分检测仪
    m_server->Get("/api/moistureBalance/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/moistureBalance/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    m_server->Get("/api/moistureBalance/query", [this](const httplib::Request& req, httplib::Response& res) {
        handleTaskQuery(req, res);
    });
    
    // 培养皿摇床
    m_server->Post("/api/shaker/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });
    
    // 框架装置
    m_server->Get("/api/frame/status", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceStatus(req, res);
    });
    
    m_server->Post("/api/frame/operation", [this](const httplib::Request& req, httplib::Response& res) {
        handleDeviceOperation(req, res);
    });

    LOG(INFO) << "Device routes setup completed";
}

void RestInterfaceDriver::handleHeaterStatusQuery(const httplib::Request& req, httplib::Response& res) {
    LOG(INFO) << "Handling heater status query for all magnetic stirrer heaters";

    try {
        json response;
        response["timestamp"] = getCurrentTimeString();

        bool allConnected = true;
        int connectedCount = 0;

        // 遍历所有heater设备（heater1-heater11）
        for (int i = 1; i <= 11; ++i) {
            std::string heaterName = "heater" + std::to_string(i);
            auto it = m_deviceControllers.find(heaterName);

            if (it != m_deviceControllers.end()) {
                try {
                    auto status = it->second->getStatus();
                    bool isConnected = (status.status == "CONNECTED" || status.status == "IDLE");
                    if (isConnected) {
                        connectedCount++;
                    } else {
                        allConnected = false;
                    }
                } catch (const std::exception& e) {
                    allConnected = false;
                }
            } else {
                allConnected = false;
            }
        }

        response["data"] = allConnected;

        res.set_content(response.dump(), "application/json");
        LOG(INFO) << "Heater status response sent: all connected = " << allConnected
                  << ", connected count = " << connectedCount << "/11";

    } catch (const std::exception& e) {
        LOG(ERROR) << "Error handling heater status query: " << e.what();
        createErrorResponse(res, "Internal server error", 500);
    }
}

void RestInterfaceDriver::handleHeaterOperation(const httplib::Request& req, httplib::Response& res) {
    LOG(INFO) << "Handling heater operation request";
    LOG(INFO) << "Request body: " << req.body;

    try {
        json requestData = json::parse(req.body);

        // 检查是否包含data.channel字段
        if (!requestData.contains("data") || !requestData["data"].contains("channel")) {
            LOG(ERROR) << "Missing 'data.channel' field in heater operation request";
            createErrorResponse(res, "Missing 'data.channel' field. Please specify heater channel (1-11)", 400);
            return;
        }

        int heaterId = requestData["data"]["channel"].get<int>();
        if (heaterId < 1 || heaterId > 11) {
            LOG(ERROR) << "Invalid heater channel: " << heaterId;
            createErrorResponse(res, "Invalid heater channel. Must be between 1 and 11", 400);
            return;
        }

        std::string heaterName = "heater" + std::to_string(heaterId);
        LOG(INFO) << "Operating heater: " << heaterName;

        auto it = m_deviceControllers.find(heaterName);
        if (it == m_deviceControllers.end()) {
            LOG(ERROR) << "Heater not found: " << heaterName;
            createErrorResponse(res, "Heater " + std::to_string(heaterId) + " not found", 404);
            return;
        }

        LOG(INFO) << "Executing operation for heater: " << heaterName;
        LOG(INFO) << "Request data: " << requestData.dump();

        auto taskInfo = it->second->executeOperation(requestData);
        LOG(INFO) << "Operation executed, task ID: " << taskInfo.taskId << ", status: " << static_cast<int>(taskInfo.status);

        json response;
        response["timestamp"] = getCurrentTimeString();
        response["heater_channel"] = heaterId;
        response["heater_name"] = heaterName;
        response["task_id"] = taskInfo.taskId;
        response["status"] = taskStatusToString(taskInfo.status);
        response["message"] = taskInfo.message;

        res.set_content(response.dump(), "application/json");
        LOG(INFO) << "Heater operation response sent successfully";

    } catch (const json::parse_error& e) {
        LOG(ERROR) << "JSON parse error: " << e.what();
        createErrorResponse(res, "Invalid JSON format", 400);
    } catch (const std::exception& e) {
        LOG(ERROR) << "Error handling heater operation: " << e.what();
        createErrorResponse(res, "Internal server error", 500);
    }
}

void RestInterfaceDriver::handleDeviceStatus(const httplib::Request& req, httplib::Response& res) {
    LOG(INFO) << "Received device status request: " << req.method << " " << req.path
              << " from " << req.remote_addr;

    auto pathComponents = parseUriPath(req.path);
    if (pathComponents.size() < 2) {
        LOG(ERROR) << "Invalid device path: " << req.path;
        createErrorResponse(res, "Invalid device path", 400);
        return;
    }

    std::string deviceName = pathComponents[1];

    // 特殊处理heater状态查询 - 返回所有磁力搅拌加热器的状态
    if (deviceName == "heater") {
        handleHeaterStatusQuery(req, res);
        return;
    }
    LOG(INFO) << "Device name extracted: " << deviceName;

    auto it = m_deviceControllers.find(deviceName);
    if (it == m_deviceControllers.end()) {
        LOG(ERROR) << "Device not found: " << deviceName;
        LOG(INFO) << "Available devices: ";
        for (const auto& pair : m_deviceControllers) {
            LOG(INFO) << "  - " << pair.first;
        }
        createErrorResponse(res, "Device not found: " + deviceName, 404);
        return;
    }

    try {
        LOG(INFO) << "Getting status for device: " << deviceName;
        DeviceStatus status = it->second->getStatus();
        LOG(INFO) << "Device status retrieved: " << status.status;

        json response;
        response["name"] = status.name;
        response["description"] = status.description;
        response["status"] = status.status;
        response["message"] = status.message;
        response["data"] = status.data;
        response["updateTime"] = status.updateTime;

        LOG(INFO) << "Sending device status response";
        createJsonResponse(res, response);
        LOG(INFO) << "Device status response sent successfully";
    } catch (const std::exception& e) {
        LOG(ERROR) << "Failed to get device status: " << e.what();
        createErrorResponse(res, "Failed to get device status: " + std::string(e.what()), 500);
    }
}

void RestInterfaceDriver::handleDeviceOperation(const httplib::Request& req, httplib::Response& res) {
    LOG(INFO) << "Received device operation request: " << req.method << " " << req.path
              << " from " << req.remote_addr;
    LOG(INFO) << "Request body: " << req.body;

    auto pathComponents = parseUriPath(req.path);
    if (pathComponents.size() < 2) {
        LOG(ERROR) << "Invalid device path: " << req.path;
        createErrorResponse(res, "Invalid device path", 400);
        return;
    }

    std::string deviceName = pathComponents[1];
    LOG(INFO) << "Device name extracted: " << deviceName;

    // 特殊处理heater操作 - 根据ID调用对应的磁力搅拌加热器
    if (deviceName == "heater") {
        handleHeaterOperation(req, res);
        return;
    }

    auto it = m_deviceControllers.find(deviceName);
    if (it == m_deviceControllers.end()) {
        LOG(ERROR) << "Device not found: " << deviceName;
        LOG(INFO) << "Available devices: ";
        for (const auto& pair : m_deviceControllers) {
            LOG(INFO) << "  - " << pair.first;
        }
        createErrorResponse(res, "Device not found: " + deviceName, 404);
        return;
    }

    try {
        json requestData;
        try {
            requestData = json::parse(req.body);
        } catch (const json::parse_error& e) {
            LOG(ERROR) << "Invalid JSON in request body: " << req.body;
            LOG(ERROR) << "JSON parse error: " << e.what();
            createErrorResponse(res, "Invalid JSON in request body", 400);
            return;
        }

        LOG(INFO) << "Executing operation for device: " << deviceName;
        LOG(INFO) << "Request data: " << requestData.dump();

        TaskInfo task = it->second->executeOperation(requestData);

        LOG(INFO) << "Operation executed, task ID: " << task.taskId
                  << ", status: " << static_cast<int>(task.status);

        // 存储任务信息
        {
            std::lock_guard<std::mutex> lock(m_tasksMutex);
            m_tasks[task.taskId] = task;
        }

        json response;
        response["taskId"] = task.taskId;
        response["action"] = task.action;
        response["status"] = static_cast<int>(task.status);
        response["message"] = task.message;
        response["data"] = task.data;
        response["updateTime"] = task.updateTime;

        createJsonResponse(res, response);
    } catch (const std::exception& e) {
        createErrorResponse(res, "Failed to execute operation: " + std::string(e.what()), 500);
    }
}

void RestInterfaceDriver::handleTaskQuery(const httplib::Request& req, httplib::Response& res) {
    // 从查询参数中获取taskId
    auto it = req.params.find("taskId");
    if (it == req.params.end()) {
        createErrorResponse(res, "Missing taskId parameter", 400);
        return;
    }

    int taskId;
    try {
        taskId = std::stoi(it->second);
    } catch (const std::exception& e) {
        createErrorResponse(res, "Invalid taskId parameter", 400);
        return;
    }

    std::lock_guard<std::mutex> lock(m_tasksMutex);
    auto taskIt = m_tasks.find(taskId);
    if (taskIt == m_tasks.end()) {
        createErrorResponse(res, "Task not found", 404);
        return;
    }

    const TaskInfo& task = taskIt->second;

    json response;
    response["taskId"] = task.taskId;
    response["action"] = task.action;
    response["status"] = static_cast<int>(task.status);
    response["message"] = task.message;
    response["data"] = task.data;
    response["updateTime"] = task.updateTime;

    createJsonResponse(res, response);
}

std::vector<std::string> RestInterfaceDriver::parseUriPath(const std::string& uri) {
    std::vector<std::string> components;
    std::string path = uri;

    // 移除查询参数
    size_t queryPos = path.find('?');
    if (queryPos != std::string::npos) {
        path = path.substr(0, queryPos);
    }

    // 移除开头的斜杠
    if (!path.empty() && path[0] == '/') {
        path = path.substr(1);
    }

    // 分割路径
    std::istringstream iss(path);
    std::string component;
    while (std::getline(iss, component, '/')) {
        if (!component.empty()) {
            components.push_back(component);
        }
    }

    return components;
}

void RestInterfaceDriver::createJsonResponse(httplib::Response& res, const json& data, int statusCode) {
    res.status = statusCode;
    res.set_header("Content-Type", "application/json");

    try {
        res.body = data.dump();

        // 验证生成的JSON是否有效
        if (res.body.empty()) {
            res.body = "{\"error\":\"Failed to serialize JSON\"}";
            res.status = 500;
        }
    } catch (const std::exception& e) {
        LOG(ERROR) << "JSON serialization error: " << e.what();
        res.body = "{\"error\":\"JSON serialization failed\"}";
        res.status = 500;
    } catch (...) {
        LOG(ERROR) << "Unknown JSON serialization error";
        res.body = "{\"error\":\"Unknown JSON error\"}";
        res.status = 500;
    }
}

void RestInterfaceDriver::createErrorResponse(httplib::Response& res, const std::string& message, int statusCode) {
    json error;
    error["error"] = message;
    error["timestamp"] = getCurrentTimeString();

    createJsonResponse(res, error, statusCode);
}

int RestInterfaceDriver::generateTaskId() {
    return m_nextTaskId++;
}

std::string RestInterfaceDriver::getCurrentTimeString() {
    try {
        auto now = std::chrono::system_clock::now();
        auto time_t = std::chrono::system_clock::to_time_t(now);

        std::tm* tm_ptr = std::localtime(&time_t);
        if (!tm_ptr) {
            return "1970-01-01 00:00:00";  // 默认时间
        }

        std::ostringstream oss;
        oss << std::put_time(tm_ptr, "%Y-%m-%d %H:%M:%S");
        std::string result = oss.str();

        // 检查结果是否合理
        if (result.empty() || result.length() > 50) {
            return "1970-01-01 00:00:00";  // 默认时间
        }

        return result;
    } catch (...) {
        return "1970-01-01 00:00:00";  // 默认时间
    }
}

std::string RestInterfaceDriver::taskStatusToString(TaskStatus status) {
    switch (status) {
        case TaskStatus::SUBMITTED:
            return "SUBMITTED";
        case TaskStatus::IN_PROGRESS:
            return "IN_PROGRESS";
        case TaskStatus::SUCCESS:
            return "SUCCESS";
        case TaskStatus::FAILED:
            return "FAILED";
        default:
            return "UNKNOWN";
    }
}

void RestInterfaceDriver::setError(const std::string& error) {
    m_lastError = error;
    LOG(ERROR) << error;
}

void RestInterfaceDriver::log(const std::string& level, const std::string& message) {
    if (m_config.enableLogging) {
        if (level == "ERROR") {
            LOG(ERROR) << message;
        } else if (level == "WARNING") {
            LOG(WARNING) << message;
        } else {
            LOG(INFO) << message;
        }
    }
}

} // namespace RestInterface
} // namespace AnalysisRobot
