﻿#include "RobotController.h"
#include "glog.h"
#include <sstream>
#include <chrono>
#include <iomanip>

namespace AnalysisRobot {
namespace Robot {

RobotController::RobotController(const std::string& name, const RobotConfig& config)
    : m_config(config)
    , m_deviceName(name)
    , m_deviceType("6轴机械臂")
    , m_initialized(false)
    , m_connected(false)
    , m_nextTaskId(1000)
    , m_monitoring(false) {
    m_driver = std::make_shared<RobotDriver>();
}

RobotController::~RobotController() {
    shutdown();
}

bool RobotController::initialize() {
    if (m_initialized) {
        return true;
    }
    
    // 初始化驱动器
    if (!m_driver->initialize(m_config)) {
        m_lastError = "机器人驱动器初始化失败: " + m_driver->getLastError();
        return false;
    }
    
    // 连接设备
    if (!m_driver->connect()) {
        m_lastError = "机器人连接失败: " + m_driver->getLastError();
        return false;
    }
    
    m_connected = true;
    m_initialized = true;
    
    // 启动监控线程
    startMonitoring();
    
    return true;
}

void RobotController::shutdown() {
    stopMonitoring();
    stopAllTaskThreads();
    
    if (m_driver && m_connected) {
        m_driver->disconnect();
        m_connected = false;
    }
    m_initialized = false;
}

DeviceStatus RobotController::getStatus() {
    DeviceStatus status;
    status.name = m_deviceName;
    status.description = m_deviceType + " 设备控制器";
    status.updateTime = getCurrentTimeString();
    
    if (!m_initialized || !m_connected) {
        status.status = "DISCONNECTED";
        status.message = "机器人未连接";
        return status;
    }
    
    // 获取驱动器状态
    RobotStatus robotStatus = m_driver->getStatus();
    switch (robotStatus) {
        case RobotStatus::IDLE:
            status.status = "IDLE";
            status.message = "Robot idle and available";
            break;
        case RobotStatus::BUSY:
            status.status = "BUSY";
            status.message = "Robot busy";
            break;
        case RobotStatus::ROBOT_ERROR:
            status.status = "FAILED";
            status.message = "Robot fault: " + m_driver->getLastError();
            break;
        case RobotStatus::EMERGENCY:
            status.status = "FAILED";
            status.message = "Robot emergency stop state";
            break;
        default:
            status.status = "UNKNOWN";
            status.message = "Unknown status";
            break;
    }
    
    // 获取位置信息
    Position pos = m_driver->getCurrentPosition();
    nlohmann::json data;
    data["x"] = pos.x;
    data["y"] = pos.y;
    data["z"] = pos.z;
    data["u"] = pos.rx;
    data["v"] = pos.ry;
    data["w"] = pos.rz;
    
    status.data = data;
    return status;
}

TaskInfo RobotController::executeOperation(const nlohmann::json& request) {
    if (!request.contains("action")) {
        return createErrorTask("缺少action参数");
    }
    
    std::string action = request["action"].get<std::string>();
    nlohmann::json data = request.value("data", nlohmann::json());
    
    if (!m_initialized || !m_connected) {
        return createErrorTask("机器人未连接");
    }
    
    if (action == "PICK_PLACE") {
        return handlePickPlaceOperation(data);
    } else if (action == "MOVE_TO") {
        return handleMoveToOperation(data);
    } else if (action == "HOME") {
        return handleHomeOperation();
    } else if (action == "STOP") {
        return handleStopOperation();
    } else if (action == "SET_IO") {
        return handleSetIOOperation(data);
    } else if (action == "GRIPPER") {
        return handleGripperOperation(data);
    } else {
        return createErrorTask("不支持的操作: " + action);
    }
}

TaskInfo RobotController::queryTask(int taskId) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        return it->second;
    }
    
    TaskInfo notFound;
    notFound.taskId = taskId;
    notFound.status = TaskStatus::FAILED;
    notFound.message = "任务未找到";
    return notFound;
}

std::string RobotController::getLastError() const {
    return m_lastError;
}

// ========== 私有方法实现 ==========

void RobotController::startMonitoring() {
    if (!m_monitoring) {
        m_monitoring = true;
        m_monitorThread = std::thread(&RobotController::monitorLoop, this);
    }
}

void RobotController::stopMonitoring() {
    if (m_monitoring) {
        m_monitoring = false;
        if (m_monitorThread.joinable()) {
            m_monitorThread.join();
        }
    }
}

void RobotController::monitorLoop() {
    while (m_monitoring) {
        // 定期检查机器人状态
        if (m_driver && m_connected) {
            RobotStatus status = m_driver->getStatus();
            if (status == RobotStatus::ROBOT_ERROR || status == RobotStatus::EMERGENCY) {
                // 处理错误状态
                LOG(ERROR) << "Robot error detected: " << m_driver->getLastError();
            }
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
}

void RobotController::stopAllTaskThreads() {
    std::lock_guard<std::mutex> lock(m_taskThreadsMutex);
    for (auto& pair : m_taskThreads) {
        if (pair.second.joinable()) {
            pair.second.join();
        }
    }
    m_taskThreads.clear();
}

void RobotController::cleanupTaskThread(int taskId) {
    std::lock_guard<std::mutex> lock(m_taskThreadsMutex);
    auto it = m_taskThreads.find(taskId);
    if (it != m_taskThreads.end()) {
        if (it->second.joinable()) {
            it->second.detach(); // 分离线程，让它自然结束
        }
        m_taskThreads.erase(it);
    }
}

int RobotController::generateTaskId() {
    return m_nextTaskId++;
}

void RobotController::updateTaskStatus(int taskId, TaskStatus status, const std::string& message, const nlohmann::json& data) {
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    auto it = m_tasks.find(taskId);
    if (it != m_tasks.end()) {
        it->second.status = status;
        it->second.message = message;
        it->second.data = data;
        it->second.updateTime = getCurrentTimeString();
    }
}

TaskInfo RobotController::createSuccessTask(const std::string& action, const nlohmann::json& data) {
    TaskInfo task;
    task.taskId = generateTaskId();
    task.action = action;
    task.status = TaskStatus::SUCCESS;
    task.message = action + " 操作成功";
    task.data = data;
    task.updateTime = getCurrentTimeString();
    
    // 存储任务
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    m_tasks[task.taskId] = task;
    
    return task;
}

TaskInfo RobotController::createErrorTask(const std::string& error) {
    TaskInfo task;
    task.taskId = generateTaskId();
    task.status = TaskStatus::FAILED;
    task.message = error;
    task.updateTime = getCurrentTimeString();
    return task;
}

std::string RobotController::getCurrentTimeString() {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

// ========== 操作处理方法 ==========

TaskInfo RobotController::handlePickPlaceOperation(const nlohmann::json& data) {
    if (!data.contains("source") || !data.contains("destination")) {
        return createErrorTask("缺少source或destination参数");
    }
    
    std::string source = data["source"].get<std::string>();
    std::string destination = data["destination"].get<std::string>();
    
    // 执行取放任务
    int driverTaskId = m_driver->executePickPlace(source, destination);
    if (driverTaskId < 0) {
        return createErrorTask("取放任务启动失败: " + m_driver->getLastError());
    }
    
    // 创建REST任务
    int taskId = generateTaskId();
    
    // 启动任务监控线程
    {
        std::lock_guard<std::mutex> lock(m_taskThreadsMutex);
        m_taskThreads[taskId] = std::thread([this, taskId, driverTaskId]() {
            monitorPickPlaceTask(taskId, driverTaskId);
        });
    }
    
    TaskInfo task;
    task.taskId = taskId;
    task.action = "PICK_PLACE";
    task.status = TaskStatus::SUBMITTED;
    task.message = "取放任务已提交";
    task.updateTime = getCurrentTimeString();
    
    // 存储任务
    std::lock_guard<std::mutex> lock(m_tasksMutex);
    m_tasks[taskId] = task;
    
    return task;
}

TaskInfo RobotController::handleMoveToOperation(const nlohmann::json& data) {
    if (!data.contains("position")) {
        return createErrorTask("缺少position参数");
    }
    
    nlohmann::json posData = data["position"];
    Position targetPos;
    targetPos.x = posData.value("x", 0.0);
    targetPos.y = posData.value("y", 0.0);
    targetPos.z = posData.value("z", 0.0);
    targetPos.rx = posData.value("rx", 0.0);
    targetPos.ry = posData.value("ry", 0.0);
    targetPos.rz = posData.value("rz", 0.0);
    
    MotionParams params;
    if (data.contains("speed")) {
        params.speed = data["speed"].get<int>();
    }
    if (data.contains("acceleration")) {
        params.acceleration = data["acceleration"].get<int>();
    }
    
    if (m_driver->moveLinear(targetPos, params)) {
        nlohmann::json responseData;
        responseData["position"] = posData;
        responseData["speed"] = params.speed;
        responseData["acceleration"] = params.acceleration;
        return createSuccessTask("MOVE_TO", responseData);
    } else {
        return createErrorTask("移动失败: " + m_driver->getLastError());
    }
}

TaskInfo RobotController::handleHomeOperation() {
    if (m_driver->home()) {
        nlohmann::json data;
        data["message"] = "回原点操作完成";
        return createSuccessTask("HOME", data);
    } else {
        return createErrorTask("回原点失败: " + m_driver->getLastError());
    }
}

TaskInfo RobotController::handleStopOperation() {
    if (m_driver->stop()) {
        nlohmann::json data;
        data["message"] = "Stop operation completed";
        return createSuccessTask("STOP", data);
    } else {
        return createErrorTask("Stop failed: " + m_driver->getLastError());
    }
}

TaskInfo RobotController::handleSetIOOperation(const nlohmann::json& data) {
    if (!data.contains("port") || !data.contains("value")) {
        return createErrorTask("Missing port or value parameter");
    }
    
    int port = data["port"].get<int>();
    bool value = data["value"].get<bool>();
    
    if (m_driver->setDigitalOutput(port, value)) {
        nlohmann::json responseData;
        responseData["port"] = port;
        responseData["value"] = value;
        return createSuccessTask("SET_IO", responseData);
    } else {
        return createErrorTask("IO setting failed: " + m_driver->getLastError());
    }
}

TaskInfo RobotController::handleGripperOperation(const nlohmann::json& data) {
    if (!data.contains("command")) {
        return createErrorTask("缺少command参数");
    }
    
    int command = data["command"];
    int force = data.value("force", 50);
    
    if (m_driver->controlGripper(command, force)) {
        nlohmann::json responseData;
        responseData["command"] = command;
        responseData["force"] = force;
        responseData["status"] = m_driver->getGripperStatus();
        return createSuccessTask("GRIPPER", responseData);
    } else {
        return createErrorTask("夹爪控制失败: " + m_driver->getLastError());
    }
}

void RobotController::monitorPickPlaceTask(int taskId, int driverTaskId) {
    while (true) {
        int status = m_driver->getTaskStatus(driverTaskId);
        double progress = m_driver->getTaskProgress();
        
        nlohmann::json data;
        data["progress"] = progress;
        
        switch (status) {
            case 0: // 空闲
                updateTaskStatus(taskId, TaskStatus::SUBMITTED, "等待执行", data);
                break;
            case 1: // 取料中
                updateTaskStatus(taskId, TaskStatus::IN_PROGRESS, "正在取料", data);
                break;
            case 2: // 移动中
                updateTaskStatus(taskId, TaskStatus::IN_PROGRESS, "正在移动", data);
                break;
            case 3: // 放料中
                updateTaskStatus(taskId, TaskStatus::IN_PROGRESS, "正在放料", data);
                break;
            case 4: // 完成
                updateTaskStatus(taskId, TaskStatus::SUCCESS, "取放任务完成", data);
                cleanupTaskThread(taskId);
                return;
            case 5: // 失败
                int errorCode = m_driver->getErrorCode();
                updateTaskStatus(taskId, TaskStatus::FAILED, 
                               "取放任务失败，错误码: " + std::to_string(errorCode), data);
                cleanupTaskThread(taskId);
                return;
        }
        
        std::this_thread::sleep_for(std::chrono::milliseconds(500));
    }
}

void RobotController::monitorMoveTask(int taskId) {
    // 移动任务监控实现
    // 这里可以根据需要实现移动任务的进度监控
}

} // namespace Robot
} // namespace AnalysisRobot
