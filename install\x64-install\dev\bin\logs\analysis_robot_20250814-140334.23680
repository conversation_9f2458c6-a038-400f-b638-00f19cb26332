Log file created at: 2025/08/14 14:03:34
Running on machine: DESKTOP-18IK6UP
Running duration (h:mm:ss): 0:00:00
Log line format: [IWEF]yyyymmdd hh:mm:ss.uuuuuu threadid file:line] msg
********* 14:03:34.459913 23752 main.cpp:281] === Analysis Robot Application ===
********* 14:03:34.460912 23752 main.cpp:282] Starting application...
********* 14:03:34.460912 23752 main.cpp:283] Executable directory: D:\newfuxios\install\x64-install\dev\bin
********* 14:03:34.460912 23752 main.cpp:284] Config file: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:03:34.461912 23752 main.cpp:285] Log directory: D:\newfuxios\install\x64-install\dev\bin\logs
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:15] Analysis Robot App created
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:24] Initializing Analysis Robot App
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:197] Initializing config manager
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:198] App config file: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:03:34.461912 23752 ConfigManager.cpp:9] ConfigManager created
********* 14:03:34.461912 23752 ConfigManager.cpp:18] Loading config from: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:03:34.461912 23752 ConfigManager.cpp:51] Config loaded successfully
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:209] Creating config path from: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:212] Getting parent path
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:215] Creating devices config path
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:218] Converting to string
********* 14:03:34.461912 23752 AnalysisRobotApp.cpp:221] Devices config file: D:\newfuxios\install\x64-install\dev\bin\config\devices.json
********* 14:03:34.462913 23752 ConfigManager.cpp:95] Loading devices config from: D:\newfuxios\install\x64-install\dev\bin\config\devices.json
********* 14:03:34.462913 23752 ConfigManager.cpp:128] Devices config loaded successfully
********* 14:03:34.462913 23752 AnalysisRobotApp.cpp:238] Config manager initialized successfully
********* 14:03:34.462913 23752 AnalysisRobotApp.cpp:243] Initializing device manager
********* 14:03:34.462913 23752 DeviceManager.cpp:30] Device manager created
********* 14:03:34.462913 23752 AnalysisRobotApp.cpp:249] Retrieved device config with 2 balances, 11 stirrer heaters
********* 14:03:34.462913 23752 DeviceManager.cpp:40] Initializing device manager
********* 14:03:34.462913 23752 DeviceManager.cpp:41] Device config contains 2 balances, 11 stirrer heaters
********* 14:03:34.462913 23752 DeviceManager.cpp:47] Found 2 balances in config
********* 14:03:34.462913 23752 DeviceManager.cpp:352] Initializing balance devices
********* 14:03:34.462913 23752 DeviceManager.cpp:353] Balance config size: 2
********* 14:03:34.462913 23752 DeviceManager.cpp:357] Processing balance 0: balance1
********* 14:03:34.462913 23752 DeviceManager.cpp:372] Initializing balance: balance1 on port: COM1
********* 14:03:34.462913 23752 DeviceManager.cpp:382] Attempting to initialize balance driver for: balance1
********* 14:03:34.462913 23752 DeviceManager.cpp:388] Balance driver initialized successfully: balance1
********* 14:03:34.463912 23752 BalanceController.cpp:11] Balance controller created
********* 14:03:34.463912 23752 DeviceManager.cpp:638] Registering device controller: balance1
********* 14:03:34.463912 23752 DeviceManager.cpp:640] Device controller registered successfully: balance1
********* 14:03:34.463912 23752 DeviceManager.cpp:641] Total registered controllers: 1
********* 14:03:34.463912 23752 DeviceManager.cpp:399] Balance device initialized: balance1
********* 14:03:34.463912 23752 DeviceManager.cpp:357] Processing balance 1: balance2
********* 14:03:34.463912 23752 DeviceManager.cpp:372] Initializing balance: balance2 on port: COM2
********* 14:03:34.463912 23752 DeviceManager.cpp:382] Attempting to initialize balance driver for: balance2
********* 14:03:34.463912 23752 DeviceManager.cpp:388] Balance driver initialized successfully: balance2
********* 14:03:34.463912 23752 BalanceController.cpp:11] Balance controller created
********* 14:03:34.463912 23752 DeviceManager.cpp:638] Registering device controller: balance2
********* 14:03:34.463912 23752 DeviceManager.cpp:640] Device controller registered successfully: balance2
********* 14:03:34.463912 23752 DeviceManager.cpp:641] Total registered controllers: 2
********* 14:03:34.463912 23752 DeviceManager.cpp:399] Balance device initialized: balance2
********* 14:03:34.463912 23752 DeviceManager.cpp:59] Found 11 stirrer heaters in config
********* 14:03:34.463912 23752 DeviceManager.cpp:406] Initializing stirrer heater devices
********* 14:03:34.463912 23752 DeviceManager.cpp:407] Stirrer heater config size: 11
********* 14:03:34.463912 23752 DeviceManager.cpp:411] Processing stirrer heater 0: heater1
********* 14:03:34.463912 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater1 on port: COM3
********* 14:03:34.463912 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D80D80
********* 14:03:34.463912 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater1
********* 14:03:34.463912 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater1
********* 14:03:34.463912 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.463912 23752 DeviceManager.cpp:638] Registering device controller: heater1
********* 14:03:34.463912 23752 DeviceManager.cpp:640] Device controller registered successfully: heater1
********* 14:03:34.463912 23752 DeviceManager.cpp:641] Total registered controllers: 3
********* 14:03:34.463912 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater1
********* 14:03:34.463912 23752 DeviceManager.cpp:411] Processing stirrer heater 1: heater2
********* 14:03:34.463912 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater2 on port: COM4
********* 14:03:34.464911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D6BCC0
********* 14:03:34.464911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater2
********* 14:03:34.464911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater2
********* 14:03:34.464911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.464911 23752 DeviceManager.cpp:638] Registering device controller: heater2
********* 14:03:34.464911 23752 DeviceManager.cpp:640] Device controller registered successfully: heater2
********* 14:03:34.464911 23752 DeviceManager.cpp:641] Total registered controllers: 4
********* 14:03:34.464911 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater2
********* 14:03:34.464911 23752 DeviceManager.cpp:411] Processing stirrer heater 2: heater3
********* 14:03:34.464911 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater3 on port: COM5
********* 14:03:34.464911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D52AC0
********* 14:03:34.464911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater3
********* 14:03:34.464911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater3
********* 14:03:34.464911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.464911 23752 DeviceManager.cpp:638] Registering device controller: heater3
********* 14:03:34.464911 23752 DeviceManager.cpp:640] Device controller registered successfully: heater3
********* 14:03:34.464911 23752 DeviceManager.cpp:641] Total registered controllers: 5
********* 14:03:34.464911 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater3
********* 14:03:34.464911 23752 DeviceManager.cpp:411] Processing stirrer heater 3: heater4
********* 14:03:34.464911 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater4 on port: COM6
********* 14:03:34.464911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D52BE0
********* 14:03:34.464911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater4
********* 14:03:34.464911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater4
********* 14:03:34.464911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.464911 23752 DeviceManager.cpp:638] Registering device controller: heater4
********* 14:03:34.464911 23752 DeviceManager.cpp:640] Device controller registered successfully: heater4
********* 14:03:34.464911 23752 DeviceManager.cpp:641] Total registered controllers: 6
********* 14:03:34.464911 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater4
********* 14:03:34.464911 23752 DeviceManager.cpp:411] Processing stirrer heater 4: heater5
********* 14:03:34.464911 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater5 on port: COM7
********* 14:03:34.464911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D4DAB0
********* 14:03:34.464911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater5
********* 14:03:34.464911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater5
********* 14:03:34.464911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.464911 23752 DeviceManager.cpp:638] Registering device controller: heater5
********* 14:03:34.464911 23752 DeviceManager.cpp:640] Device controller registered successfully: heater5
********* 14:03:34.465911 23752 DeviceManager.cpp:641] Total registered controllers: 7
********* 14:03:34.465911 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater5
********* 14:03:34.465911 23752 DeviceManager.cpp:411] Processing stirrer heater 5: heater6
********* 14:03:34.465911 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater6 on port: COM8
********* 14:03:34.465911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D4DBD0
********* 14:03:34.465911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater6
********* 14:03:34.465911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater6
********* 14:03:34.465911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.465911 23752 DeviceManager.cpp:638] Registering device controller: heater6
********* 14:03:34.465911 23752 DeviceManager.cpp:640] Device controller registered successfully: heater6
********* 14:03:34.465911 23752 DeviceManager.cpp:641] Total registered controllers: 8
********* 14:03:34.465911 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater6
********* 14:03:34.465911 23752 DeviceManager.cpp:411] Processing stirrer heater 6: heater7
********* 14:03:34.465911 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater7 on port: COM9
********* 14:03:34.465911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D4DCF0
********* 14:03:34.465911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater7
********* 14:03:34.465911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater7
********* 14:03:34.465911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.465911 23752 DeviceManager.cpp:638] Registering device controller: heater7
********* 14:03:34.465911 23752 DeviceManager.cpp:640] Device controller registered successfully: heater7
********* 14:03:34.465911 23752 DeviceManager.cpp:641] Total registered controllers: 9
********* 14:03:34.465911 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater7
********* 14:03:34.465911 23752 DeviceManager.cpp:411] Processing stirrer heater 7: heater8
********* 14:03:34.465911 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater8 on port: COM10
********* 14:03:34.465911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93D7B4C0
********* 14:03:34.465911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater8
********* 14:03:34.465911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater8
********* 14:03:34.465911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.465911 23752 DeviceManager.cpp:638] Registering device controller: heater8
********* 14:03:34.465911 23752 DeviceManager.cpp:640] Device controller registered successfully: heater8
********* 14:03:34.465911 23752 DeviceManager.cpp:641] Total registered controllers: 10
********* 14:03:34.465911 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater8
********* 14:03:34.465911 23752 DeviceManager.cpp:411] Processing stirrer heater 8: heater9
********* 14:03:34.465911 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater9 on port: COM11
********* 14:03:34.465911 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93DE9A30
********* 14:03:34.465911 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater9
********* 14:03:34.465911 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater9
********* 14:03:34.465911 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.466912 23752 DeviceManager.cpp:638] Registering device controller: heater9
********* 14:03:34.466912 23752 DeviceManager.cpp:640] Device controller registered successfully: heater9
********* 14:03:34.466912 23752 DeviceManager.cpp:641] Total registered controllers: 11
********* 14:03:34.466912 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater9
********* 14:03:34.466912 23752 DeviceManager.cpp:411] Processing stirrer heater 9: heater10
********* 14:03:34.466912 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater10 on port: COM12
********* 14:03:34.467912 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93DE9EB0
********* 14:03:34.467912 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater10
********* 14:03:34.467912 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater10
********* 14:03:34.467912 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.467912 23752 DeviceManager.cpp:638] Registering device controller: heater10
********* 14:03:34.467912 23752 DeviceManager.cpp:640] Device controller registered successfully: heater10
********* 14:03:34.467912 23752 DeviceManager.cpp:641] Total registered controllers: 12
********* 14:03:34.467912 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater10
********* 14:03:34.467912 23752 DeviceManager.cpp:411] Processing stirrer heater 10: heater11
********* 14:03:34.467912 23752 DeviceManager.cpp:426] Initializing stirrer heater: heater11 on port: COM13
********* 14:03:34.467912 23752 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 0000027A93DE97F0
********* 14:03:34.467912 23752 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater11
********* 14:03:34.468912 23752 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater11
********* 14:03:34.468912 23752 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:03:34.468912 23752 DeviceManager.cpp:638] Registering device controller: heater11
********* 14:03:34.468912 23752 DeviceManager.cpp:640] Device controller registered successfully: heater11
********* 14:03:34.468912 23752 DeviceManager.cpp:641] Total registered controllers: 13
********* 14:03:34.468912 23752 DeviceManager.cpp:453] Stirrer heater device initialized: heater11
********* 14:03:34.468912 23752 DeviceManager.cpp:71] Found moisture analyzer in config
********* 14:03:34.468912 23752 DeviceManager.cpp:460] Initializing moisture analyzer
********* 14:03:34.468912 23752 MoistureAnalyzerController.cpp:14] Moisture analyzer controller created
********* 14:03:34.468912 23752 DeviceManager.cpp:638] Registering device controller: moistureBalance
********* 14:03:34.468912 23752 DeviceManager.cpp:640] Device controller registered successfully: moistureBalance
********* 14:03:34.468912 23752 DeviceManager.cpp:641] Total registered controllers: 14
********* 14:03:34.468912 23752 DeviceManager.cpp:489] Moisture analyzer initialized: moistureBalance
********* 14:03:34.468912 23752 DeviceManager.cpp:83] Found 2 robots in config
********* 14:03:34.468912 23752 DeviceManager.cpp:494] Initializing robot devices
********* 14:03:34.468912 23752 DeviceManager.cpp:522] Attempting to initialize robot driver for: robot at 192.168.1.100:502
********* 14:03:34.468912 23752 DeviceManager.cpp:541] Exception while initializing robot robot: device or resource busy: device or resource busy
********* 14:03:34.469913 23752 DeviceManager.cpp:522] Attempting to initialize robot driver for: robot2 at 192.168.1.101:502
********* 14:03:34.469913 23752 DeviceManager.cpp:541] Exception while initializing robot robot2: device or resource busy: device or resource busy
********* 14:03:34.470919 23752 DeviceManager.cpp:552] No robots were successfully initialized
********* 14:03:34.470919 23752 DeviceManager.cpp:85] Failed to initialize robot devices: 
********* 14:03:34.470919 23752 DeviceManager.cpp:86] Continuing initialization without robot devices
********* 14:03:34.470919 23752 DeviceManager.cpp:558] Initializing other devices
********* 14:03:34.470919 23752 SampleEntryController.cpp:13] Sample entry controller created
********* 14:03:34.470919 23752 DeviceManager.cpp:638] Registering device controller: sampleEntry
********* 14:03:34.470919 23752 DeviceManager.cpp:640] Device controller registered successfully: sampleEntry
********* 14:03:34.470919 23752 DeviceManager.cpp:641] Total registered controllers: 15
********* 14:03:34.470919 23752 DeviceManager.cpp:563] Sample entry controller initialized
********* 14:03:34.470919 23752 SampleExitController.cpp:12] Sample exit controller created
********* 14:03:34.470919 23752 DeviceManager.cpp:638] Registering device controller: sampleExit
********* 14:03:34.470919 23752 DeviceManager.cpp:640] Device controller registered successfully: sampleExit
********* 14:03:34.470919 23752 DeviceManager.cpp:641] Total registered controllers: 16
********* 14:03:34.470919 23752 DeviceManager.cpp:568] Sample exit controller initialized
********* 14:03:34.470919 23752 RepoController.cpp:12] Repo controller created
********* 14:03:34.470919 23752 DeviceManager.cpp:638] Registering device controller: repo
********* 14:03:34.470919 23752 DeviceManager.cpp:640] Device controller registered successfully: repo
********* 14:03:34.470919 23752 DeviceManager.cpp:641] Total registered controllers: 17
********* 14:03:34.470919 23752 DeviceManager.cpp:573] Repo controller initialized
********* 14:03:34.470919 23752 DosingController.cpp:12] Dosing controller created
********* 14:03:34.470919 23752 DeviceManager.cpp:638] Registering device controller: dosing
********* 14:03:34.470919 23752 DeviceManager.cpp:640] Device controller registered successfully: dosing
********* 14:03:34.470919 23752 DeviceManager.cpp:641] Total registered controllers: 18
********* 14:03:34.470919 23752 DeviceManager.cpp:578] Dosing controller initialized
********* 14:03:34.471913 23752 VolumeController.cpp:12] Volume controller created
********* 14:03:34.471913 23752 DeviceManager.cpp:638] Registering device controller: volume
********* 14:03:34.471913 23752 DeviceManager.cpp:640] Device controller registered successfully: volume
********* 14:03:34.471913 23752 DeviceManager.cpp:641] Total registered controllers: 19
********* 14:03:34.471913 23752 DeviceManager.cpp:583] Volume controller initialized
********* 14:03:34.471913 23752 FilterController.cpp:12] Filter controller created
********* 14:03:34.471913 23752 DeviceManager.cpp:638] Registering device controller: filter
********* 14:03:34.471913 23752 DeviceManager.cpp:640] Device controller registered successfully: filter
********* 14:03:34.471913 23752 DeviceManager.cpp:641] Total registered controllers: 20
********* 14:03:34.471913 23752 DeviceManager.cpp:588] Filter controller initialized
********* 14:03:34.471913 23752 StirController.cpp:11] Stir controller created
********* 14:03:34.471913 23752 DeviceManager.cpp:638] Registering device controller: stir
********* 14:03:34.471913 23752 DeviceManager.cpp:640] Device controller registered successfully: stir
********* 14:03:34.471913 23752 DeviceManager.cpp:641] Total registered controllers: 21
********* 14:03:34.471913 23752 DeviceManager.cpp:593] Stir controller initialized
********* 14:03:34.471913 23752 ICPEntryController.cpp:11] ICP entry controller created
********* 14:03:34.471913 23752 DeviceManager.cpp:638] Registering device controller: icpEntry
********* 14:03:34.471913 23752 DeviceManager.cpp:640] Device controller registered successfully: icpEntry
********* 14:03:34.471913 23752 DeviceManager.cpp:641] Total registered controllers: 22
********* 14:03:34.471913 23752 DeviceManager.cpp:598] ICP entry controller initialized
********* 14:03:34.471913 23752 ShakerController.cpp:11] Shaker controller created
********* 14:03:34.471913 23752 DeviceManager.cpp:638] Registering device controller: shaker
********* 14:03:34.471913 23752 DeviceManager.cpp:640] Device controller registered successfully: shaker
********* 14:03:34.471913 23752 DeviceManager.cpp:641] Total registered controllers: 23
********* 14:03:34.471913 23752 DeviceManager.cpp:603] Shaker controller initialized
********* 14:03:34.471913 23752 FrameController.cpp:11] Frame controller created
********* 14:03:34.471913 23752 DeviceManager.cpp:638] Registering device controller: frame
********* 14:03:34.471913 23752 DeviceManager.cpp:640] Device controller registered successfully: frame
********* 14:03:34.471913 23752 DeviceManager.cpp:641] Total registered controllers: 24
********* 14:03:34.471913 23752 DeviceManager.cpp:608] Frame controller initialized
********* 14:03:34.471913 23752 PouringController.cpp:19] Pouring controller created
********* 14:03:34.471913 23752 PouringController.cpp:319] Balance or axis driver not provided
********* 14:03:34.471913 23752 PouringController.cpp:23] Failed to initialize pouring algorithm
********* 14:03:34.472911 23752 DeviceManager.cpp:638] Registering device controller: pouring
********* 14:03:34.472911 23752 DeviceManager.cpp:640] Device controller registered successfully: pouring
********* 14:03:34.472911 23752 DeviceManager.cpp:641] Total registered controllers: 25
********* 14:03:34.472911 23752 DeviceManager.cpp:628] Pouring controller initialized
********* 14:03:34.472911 23752 DeviceManager.cpp:99] Device manager initialized successfully
********* 14:03:34.472911 23752 AnalysisRobotApp.cpp:257] Device manager initialized successfully
********* 14:03:34.472911 23752 AnalysisRobotApp.cpp:262] Initializing REST server
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:24] Initializing REST interface driver
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:25] Config - Host: 0.0.0.0, Port: 8080
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:26] Config - Max connections: 100
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:27] Config - Thread pool size: 4
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:32] Creating HTTP server
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:41] Initializing default routes
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:141] Initializing default routes
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:179] Setting up device routes
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:185] Setting up device routes
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:352] Device routes setup completed
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:181] Default routes initialized successfully
********* 14:03:34.472911 23752 RestInterfaceDriver.cpp:44] REST interface driver initialized successfully
********* 14:03:34.472911 23752 AnalysisRobotApp.cpp:281] REST server initialized successfully
********* 14:03:34.472911 23752 AnalysisRobotApp.cpp:286] Registering device controllers
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:289] Retrieved 25 device controllers from device manager
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:297] Registering device controller: balance1
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:85] Registering device controller: balance1
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: balance1
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:88] Total registered devices: 1
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: balance1
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:297] Registering device controller: balance2
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:85] Registering device controller: balance2
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: balance2
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:88] Total registered devices: 2
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: balance2
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:297] Registering device controller: dosing
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:85] Registering device controller: dosing
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: dosing
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:88] Total registered devices: 3
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: dosing
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:297] Registering device controller: filter
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:85] Registering device controller: filter
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: filter
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:88] Total registered devices: 4
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: filter
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:297] Registering device controller: frame
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:85] Registering device controller: frame
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: frame
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:88] Total registered devices: 5
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: frame
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater1
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater1
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater1
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:88] Total registered devices: 6
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater1
********* 14:03:34.473912 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater10
********* 14:03:34.473912 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater10
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater10
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 7
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater10
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater11
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater11
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater11
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 8
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater11
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater2
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater2
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater2
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 9
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater2
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater3
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater3
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater3
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 10
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater3
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater4
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater4
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater4
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 11
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater4
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater5
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater5
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater5
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 12
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater5
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater6
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater6
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater6
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 13
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater6
********* 14:03:34.474911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater7
********* 14:03:34.474911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater7
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater7
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 14
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater7
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater8
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater8
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater8
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 15
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater8
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:297] Registering device controller: heater9
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:85] Registering device controller: heater9
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater9
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 16
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater9
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:297] Registering device controller: icpEntry
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:85] Registering device controller: icpEntry
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: icpEntry
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 17
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: icpEntry
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:297] Registering device controller: moistureBalance
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:85] Registering device controller: moistureBalance
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: moistureBalance
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 18
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: moistureBalance
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:297] Registering device controller: pouring
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:85] Registering device controller: pouring
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: pouring
********* 14:03:34.475911 23752 RestInterfaceDriver.cpp:88] Total registered devices: 19
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: pouring
********* 14:03:34.475911 23752 AnalysisRobotApp.cpp:297] Registering device controller: repo
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:85] Registering device controller: repo
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: repo
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:88] Total registered devices: 20
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: repo
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:297] Registering device controller: sampleEntry
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:85] Registering device controller: sampleEntry
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: sampleEntry
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:88] Total registered devices: 21
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: sampleEntry
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:297] Registering device controller: sampleExit
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:85] Registering device controller: sampleExit
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: sampleExit
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:88] Total registered devices: 22
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: sampleExit
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:297] Registering device controller: shaker
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:85] Registering device controller: shaker
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: shaker
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:88] Total registered devices: 23
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: shaker
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:297] Registering device controller: stir
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:85] Registering device controller: stir
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: stir
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:88] Total registered devices: 24
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: stir
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:297] Registering device controller: volume
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:85] Registering device controller: volume
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:87] Device controller registered successfully: volume
********* 14:03:34.476914 23752 RestInterfaceDriver.cpp:88] Total registered devices: 25
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:299] Successfully registered device controller: volume
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:302] All 25 device controllers registered successfully
********* 14:03:34.476914 23752 AnalysisRobotApp.cpp:52] Analysis Robot App initialized successfully
********* 14:03:34.476914 23752 main.cpp:297] Application initialized successfully
********* 14:03:34.476914 23752 main.cpp:135] Performing initial log cleanup...
********* 14:03:34.477912 23752 main.cpp:92] Log cleanup completed: no old files to delete
********* 14:03:34.477912 23752 main.cpp:142] Log cleanup thread started successfully
********* 14:03:34.477912 23752 AnalysisRobotApp.cpp:72] Starting Analysis Robot App
********* 14:03:34.477912 23752 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:03:34.477912 23752 DeviceManager.cpp:145] Attempting to connect balance device 0
********* 14:03:34.478914 23776 main.cpp:105] Log cleanup thread started, checking every 24 hours
********* 14:03:34.479912 23752 DeviceManager.cpp:151] Balance device 0 connected successfully
********* 14:03:34.479912 23752 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:03:34.479912 23752 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:03:34.479912 23752 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:03:34.479912 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:03:39.720021 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:03:39.720021 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:03:39.720021 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 1
********* 14:03:39.721020 23752 HeatingMagneticStirrerDriver.cpp:99] Serial port COM4 opened successfully at 0000027A93D6BCC0
********* 14:03:39.721020 23752 DeviceManager.cpp:172] Stirrer heater device 1 connected successfully
********* 14:03:39.721020 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 2
********* 14:03:39.787487 23752 HeatingMagneticStirrerDriver.cpp:99] Serial port COM5 opened successfully at 0000027A93D52AC0
********* 14:03:39.787487 23752 DeviceManager.cpp:172] Stirrer heater device 2 connected successfully
********* 14:03:39.787487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:03:39.787487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:03:39.787487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:03:39.787487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:03:39.787487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:03:39.787487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:03:39.787487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:03:39.787487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:03:39.788487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:03:39.788487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:03:39.788487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:03:39.788487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:03:39.788487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:03:39.788487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:03:39.788487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:03:39.788487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:03:39.788487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:03:39.788487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:03:39.788487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:03:39.788487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:03:39.788487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:03:39.788487 23752 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:03:39.788487 23752 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:03:39.788487 23752 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:03:39.788487 23752 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:03:39.788487 23752 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:03:39.788487 23752 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:03:39.789486 23752 DeviceManager.cpp:221] Device connection summary: 0 already connected, 3 newly connected, 11 failed
********* 14:03:39.789486 23752 DeviceManager.cpp:229] Some devices failed to connect
********* 14:03:39.790028 23752 AnalysisRobotApp.cpp:77] Some devices failed to connect, continuing anyway
********* 14:03:39.790028 23752 DeviceManager.cpp:112] Starting all devices
********* 14:03:39.790028 23752 DeviceManager.cpp:117] All devices started
********* 14:03:39.790028 23752 RestInterfaceDriver.cpp:62] REST server started on 0.0.0.0:8080
********* 14:03:39.790028 23752 AnalysisRobotApp.cpp:96] Analysis Robot App started successfully
********* 14:03:39.790028 23580 RestInterfaceDriver.cpp:120] Server loop started
********* 14:03:39.790028 23752 AnalysisRobotApp.cpp:144] Analysis Robot App is running. Press Ctrl+C to stop.
********* 14:03:39.790028 23580 RestInterfaceDriver.cpp:121] Starting HTTP server on 0.0.0.0:8080
********* 14:03:39.790028 22756 AnalysisRobotApp.cpp:307] Monitor thread started
********* 14:03:39.790028 23580 RestInterfaceDriver.cpp:124] Server object exists, calling listen()
********* 14:03:49.850768 22756 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:03:49.850768 22756 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:03:49.850768 22756 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:03:49.850768 22756 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:03:49.850768 22756 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:03:49.850768 22756 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:03:49.850768 22756 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:03:53.627786 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:03:53.627786 22756 HeatingMagneticStirrerDriver.cpp:494] Read failure 1/5: Failed to send read request after 3 retries
********* 14:03:57.957116  4828 RestInterfaceDriver.cpp:149] Received health check request from 127.0.0.1
********* 14:03:57.957116  4828 RestInterfaceDriver.cpp:155] Health check response prepared with timestamp: 2025-08-14 14:03:57
********* 14:03:57.958117  4828 RestInterfaceDriver.cpp:157] Health check response sent successfully
********* 14:03:58.694537 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: No response from device after 3 retries
********* 14:03:58.694537 22756 HeatingMagneticStirrerDriver.cpp:494] Read failure 2/5: No response from device after 3 retries
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:03:58.695540 22756 DeviceManager.cpp:341] Device connection status: 4/25 connected
********* 14:03:58.695540 22756 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:03:58.696537 22756 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 70 seconds ago)
********* 14:03:58.696537 22756 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:03:58.696537 22756 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:03:58.696537 22756 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:03:58.696537 22756 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:03:58.696537 22756 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:03:58.696537 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:04:00.016755 22416 RestInterfaceDriver.cpp:474] Received device status request: GET /api/heater/status from 127.0.0.1
********* 14:04:00.026652 22416 RestInterfaceDriver.cpp:356] Handling heater status query for all magnetic stirrer heaters
********* 14:04:03.812089 22416 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:04:03.812089 22416 HeatingMagneticStirrerDriver.cpp:494] Read failure 3/5: Failed to send read request after 3 retries
********* 14:04:03.941625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:04:03.941625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:04:03.941625 22756 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:04:03.941625 22756 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:04:03.941625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:04:03.941625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:04:03.941625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:04:03.941625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:04:03.941625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:04:03.941625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:04:03.941625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:04:03.941625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:04:03.941625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:04:03.941625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:04:03.941625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:04:03.941625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:04:03.941625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:04:03.941625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:04:03.941625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:04:03.942625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:04:03.942625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:04:03.942625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:04:03.942625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:04:03.942625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:04:03.942625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:04:03.942625 22756 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:04:03.942625 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:04:03.942625 22756 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:04:03.942625 22756 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:04:03.942625 22756 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:04:03.942625 22756 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:04:03.942625 22756 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:04:03.942625 22756 DeviceManager.cpp:229] Some devices failed to connect
********* 14:04:04.049638 22756 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:04:04.049638 22756 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:04:04.049638 22756 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:04:04.049638 22756 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:04:04.049638 22756 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:04:04.049638 22756 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:04:04.049638 22756 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:04:07.829545 22756 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:04:07.829545 22756 HeatingMagneticStirrerDriver.cpp:494] Read failure 4/5: Failed to send read request after 3 retries
