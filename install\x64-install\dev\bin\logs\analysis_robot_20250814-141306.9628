Log file created at: 2025/08/14 14:13:06
Running on machine: DESKTOP-18IK6UP
Running duration (h:mm:ss): 0:00:00
Log line format: [IWEF]yyyymmdd hh:mm:ss.uuuuuu threadid file:line] msg
********* 14:13:06.106837  3664 main.cpp:281] === Analysis Robot Application ===
********* 14:13:06.107838  3664 main.cpp:282] Starting application...
********* 14:13:06.107838  3664 main.cpp:283] Executable directory: D:\newfuxios\install\x64-install\dev\bin
********* 14:13:06.107838  3664 main.cpp:284] Config file: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:13:06.107838  3664 main.cpp:285] Log directory: D:\newfuxios\install\x64-install\dev\bin\logs
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:15] Analysis Robot App created
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:24] Initializing Analysis Robot App
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:197] Initializing config manager
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:198] App config file: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:13:06.108839  3664 ConfigManager.cpp:9] ConfigManager created
********* 14:13:06.108839  3664 ConfigManager.cpp:18] Loading config from: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:13:06.108839  3664 ConfigManager.cpp:51] Config loaded successfully
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:209] Creating config path from: D:\newfuxios\install\x64-install\dev\bin\config/app.json
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:212] Getting parent path
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:215] Creating devices config path
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:218] Converting to string
********* 14:13:06.108839  3664 AnalysisRobotApp.cpp:221] Devices config file: D:\newfuxios\install\x64-install\dev\bin\config\devices.json
********* 14:13:06.108839  3664 ConfigManager.cpp:95] Loading devices config from: D:\newfuxios\install\x64-install\dev\bin\config\devices.json
********* 14:13:06.109838  3664 ConfigManager.cpp:128] Devices config loaded successfully
********* 14:13:06.109838  3664 AnalysisRobotApp.cpp:238] Config manager initialized successfully
********* 14:13:06.109838  3664 AnalysisRobotApp.cpp:243] Initializing device manager
********* 14:13:06.109838  3664 DeviceManager.cpp:30] Device manager created
********* 14:13:06.109838  3664 AnalysisRobotApp.cpp:249] Retrieved device config with 2 balances, 11 stirrer heaters
********* 14:13:06.109838  3664 DeviceManager.cpp:40] Initializing device manager
********* 14:13:06.109838  3664 DeviceManager.cpp:41] Device config contains 2 balances, 11 stirrer heaters
********* 14:13:06.109838  3664 DeviceManager.cpp:47] Found 2 balances in config
********* 14:13:06.109838  3664 DeviceManager.cpp:352] Initializing balance devices
********* 14:13:06.109838  3664 DeviceManager.cpp:353] Balance config size: 2
********* 14:13:06.109838  3664 DeviceManager.cpp:357] Processing balance 0: balance1
********* 14:13:06.110837  3664 DeviceManager.cpp:372] Initializing balance: balance1 on port: COM1
********* 14:13:06.110837  3664 DeviceManager.cpp:382] Attempting to initialize balance driver for: balance1
********* 14:13:06.110837  3664 DeviceManager.cpp:388] Balance driver initialized successfully: balance1
********* 14:13:06.110837  3664 BalanceController.cpp:11] Balance controller created
********* 14:13:06.110837  3664 DeviceManager.cpp:638] Registering device controller: balance1
********* 14:13:06.110837  3664 DeviceManager.cpp:640] Device controller registered successfully: balance1
********* 14:13:06.110837  3664 DeviceManager.cpp:641] Total registered controllers: 1
********* 14:13:06.110837  3664 DeviceManager.cpp:399] Balance device initialized: balance1
********* 14:13:06.110837  3664 DeviceManager.cpp:357] Processing balance 1: balance2
********* 14:13:06.110837  3664 DeviceManager.cpp:372] Initializing balance: balance2 on port: COM2
********* 14:13:06.110837  3664 DeviceManager.cpp:382] Attempting to initialize balance driver for: balance2
********* 14:13:06.110837  3664 DeviceManager.cpp:388] Balance driver initialized successfully: balance2
********* 14:13:06.110837  3664 BalanceController.cpp:11] Balance controller created
********* 14:13:06.110837  3664 DeviceManager.cpp:638] Registering device controller: balance2
********* 14:13:06.110837  3664 DeviceManager.cpp:640] Device controller registered successfully: balance2
********* 14:13:06.110837  3664 DeviceManager.cpp:641] Total registered controllers: 2
********* 14:13:06.110837  3664 DeviceManager.cpp:399] Balance device initialized: balance2
********* 14:13:06.110837  3664 DeviceManager.cpp:59] Found 11 stirrer heaters in config
********* 14:13:06.110837  3664 DeviceManager.cpp:406] Initializing stirrer heater devices
********* 14:13:06.110837  3664 DeviceManager.cpp:407] Stirrer heater config size: 11
********* 14:13:06.111838  3664 DeviceManager.cpp:411] Processing stirrer heater 0: heater1
********* 14:13:06.111838  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater1 on port: COM3
********* 14:13:06.111838  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5F20
********* 14:13:06.111838  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater1
********* 14:13:06.111838  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater1
********* 14:13:06.111838  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.111838  3664 DeviceManager.cpp:638] Registering device controller: heater1
********* 14:13:06.111838  3664 DeviceManager.cpp:640] Device controller registered successfully: heater1
********* 14:13:06.111838  3664 DeviceManager.cpp:641] Total registered controllers: 3
********* 14:13:06.111838  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater1
********* 14:13:06.111838  3664 DeviceManager.cpp:411] Processing stirrer heater 1: heater2
********* 14:13:06.111838  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater2 on port: COM4
********* 14:13:06.111838  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5BC0
********* 14:13:06.111838  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater2
********* 14:13:06.112838  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater2
********* 14:13:06.112838  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.112838  3664 DeviceManager.cpp:638] Registering device controller: heater2
********* 14:13:06.112838  3664 DeviceManager.cpp:640] Device controller registered successfully: heater2
********* 14:13:06.112838  3664 DeviceManager.cpp:641] Total registered controllers: 4
********* 14:13:06.112838  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater2
********* 14:13:06.112838  3664 DeviceManager.cpp:411] Processing stirrer heater 2: heater3
********* 14:13:06.112838  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater3 on port: COM5
********* 14:13:06.112838  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5980
********* 14:13:06.112838  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater3
********* 14:13:06.112838  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater3
********* 14:13:06.112838  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.112838  3664 DeviceManager.cpp:638] Registering device controller: heater3
********* 14:13:06.112838  3664 DeviceManager.cpp:640] Device controller registered successfully: heater3
********* 14:13:06.112838  3664 DeviceManager.cpp:641] Total registered controllers: 5
********* 14:13:06.113837  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater3
********* 14:13:06.113837  3664 DeviceManager.cpp:411] Processing stirrer heater 3: heater4
********* 14:13:06.113837  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater4 on port: COM6
********* 14:13:06.113837  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5620
********* 14:13:06.113837  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater4
********* 14:13:06.113837  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater4
********* 14:13:06.113837  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.113837  3664 DeviceManager.cpp:638] Registering device controller: heater4
********* 14:13:06.113837  3664 DeviceManager.cpp:640] Device controller registered successfully: heater4
********* 14:13:06.113837  3664 DeviceManager.cpp:641] Total registered controllers: 6
********* 14:13:06.113837  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater4
********* 14:13:06.113837  3664 DeviceManager.cpp:411] Processing stirrer heater 4: heater5
********* 14:13:06.113837  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater5 on port: COM7
********* 14:13:06.114838  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5740
********* 14:13:06.114838  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater5
********* 14:13:06.114838  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater5
********* 14:13:06.114838  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.114838  3664 DeviceManager.cpp:638] Registering device controller: heater5
********* 14:13:06.114838  3664 DeviceManager.cpp:640] Device controller registered successfully: heater5
********* 14:13:06.114838  3664 DeviceManager.cpp:641] Total registered controllers: 7
********* 14:13:06.114838  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater5
********* 14:13:06.114838  3664 DeviceManager.cpp:411] Processing stirrer heater 5: heater6
********* 14:13:06.114838  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater6 on port: COM8
********* 14:13:06.114838  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F53E0
********* 14:13:06.114838  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater6
********* 14:13:06.114838  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater6
********* 14:13:06.114838  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.114838  3664 DeviceManager.cpp:638] Registering device controller: heater6
********* 14:13:06.115839  3664 DeviceManager.cpp:640] Device controller registered successfully: heater6
********* 14:13:06.115839  3664 DeviceManager.cpp:641] Total registered controllers: 8
********* 14:13:06.115839  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater6
********* 14:13:06.115839  3664 DeviceManager.cpp:411] Processing stirrer heater 6: heater7
********* 14:13:06.115839  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater7 on port: COM9
********* 14:13:06.115839  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5CE0
********* 14:13:06.115839  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater7
********* 14:13:06.115839  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater7
********* 14:13:06.115839  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.115839  3664 DeviceManager.cpp:638] Registering device controller: heater7
********* 14:13:06.115839  3664 DeviceManager.cpp:640] Device controller registered successfully: heater7
********* 14:13:06.115839  3664 DeviceManager.cpp:641] Total registered controllers: 9
********* 14:13:06.115839  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater7
********* 14:13:06.115839  3664 DeviceManager.cpp:411] Processing stirrer heater 7: heater8
********* 14:13:06.115839  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater8 on port: COM10
********* 14:13:06.115839  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5E00
********* 14:13:06.116837  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater8
********* 14:13:06.116837  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater8
********* 14:13:06.116837  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.116837  3664 DeviceManager.cpp:638] Registering device controller: heater8
********* 14:13:06.116837  3664 DeviceManager.cpp:640] Device controller registered successfully: heater8
********* 14:13:06.116837  3664 DeviceManager.cpp:641] Total registered controllers: 10
********* 14:13:06.116837  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater8
********* 14:13:06.116837  3664 DeviceManager.cpp:411] Processing stirrer heater 8: heater9
********* 14:13:06.116837  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater9 on port: COM11
********* 14:13:06.116837  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5500
********* 14:13:06.116837  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater9
********* 14:13:06.116837  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater9
********* 14:13:06.116837  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.116837  3664 DeviceManager.cpp:638] Registering device controller: heater9
********* 14:13:06.116837  3664 DeviceManager.cpp:640] Device controller registered successfully: heater9
********* 14:13:06.116837  3664 DeviceManager.cpp:641] Total registered controllers: 11
********* 14:13:06.116837  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater9
********* 14:13:06.116837  3664 DeviceManager.cpp:411] Processing stirrer heater 9: heater10
********* 14:13:06.116837  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater10 on port: COM12
********* 14:13:06.116837  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F5860
********* 14:13:06.116837  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater10
********* 14:13:06.116837  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater10
********* 14:13:06.116837  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.117837  3664 DeviceManager.cpp:638] Registering device controller: heater10
********* 14:13:06.117837  3664 DeviceManager.cpp:640] Device controller registered successfully: heater10
********* 14:13:06.117837  3664 DeviceManager.cpp:641] Total registered controllers: 12
********* 14:13:06.117837  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater10
********* 14:13:06.117837  3664 DeviceManager.cpp:411] Processing stirrer heater 10: heater11
********* 14:13:06.117837  3664 DeviceManager.cpp:426] Initializing stirrer heater: heater11 on port: COM13
********* 14:13:06.117837  3664 HeatingMagneticStirrerDriver.cpp:20] HeatingMagneticStirrerDriver created at 000001C9822F6040
********* 14:13:06.117837  3664 DeviceManager.cpp:436] Attempting to initialize stirrer heater driver for: heater11
********* 14:13:06.117837  3664 DeviceManager.cpp:442] Stirrer heater driver initialized successfully: heater11
********* 14:13:06.117837  3664 HeatingMagneticStirrerController.cpp:15] Heating magnetic stirrer controller created
********* 14:13:06.117837  3664 DeviceManager.cpp:638] Registering device controller: heater11
********* 14:13:06.117837  3664 DeviceManager.cpp:640] Device controller registered successfully: heater11
********* 14:13:06.117837  3664 DeviceManager.cpp:641] Total registered controllers: 13
********* 14:13:06.117837  3664 DeviceManager.cpp:453] Stirrer heater device initialized: heater11
********* 14:13:06.117837  3664 DeviceManager.cpp:71] Found moisture analyzer in config
********* 14:13:06.117837  3664 DeviceManager.cpp:460] Initializing moisture analyzer
********* 14:13:06.117837  3664 MoistureAnalyzerController.cpp:14] Moisture analyzer controller created
********* 14:13:06.117837  3664 DeviceManager.cpp:638] Registering device controller: moistureBalance
********* 14:13:06.117837  3664 DeviceManager.cpp:640] Device controller registered successfully: moistureBalance
********* 14:13:06.117837  3664 DeviceManager.cpp:641] Total registered controllers: 14
********* 14:13:06.117837  3664 DeviceManager.cpp:489] Moisture analyzer initialized: moistureBalance
********* 14:13:06.118837  3664 DeviceManager.cpp:83] Found 2 robots in config
********* 14:13:06.118837  3664 DeviceManager.cpp:494] Initializing robot devices
********* 14:13:06.118837  3664 DeviceManager.cpp:522] Attempting to initialize robot driver for: robot at 192.168.1.100:502
********* 14:13:06.118837  3664 DeviceManager.cpp:541] Exception while initializing robot robot: device or resource busy: device or resource busy
********* 14:13:06.119838  3664 DeviceManager.cpp:522] Attempting to initialize robot driver for: robot2 at 192.168.1.101:502
********* 14:13:06.119838  3664 DeviceManager.cpp:541] Exception while initializing robot robot2: device or resource busy: device or resource busy
********* 14:13:06.120837  3664 DeviceManager.cpp:552] No robots were successfully initialized
********* 14:13:06.120837  3664 DeviceManager.cpp:85] Failed to initialize robot devices: 
********* 14:13:06.120837  3664 DeviceManager.cpp:86] Continuing initialization without robot devices
********* 14:13:06.120837  3664 DeviceManager.cpp:558] Initializing other devices
********* 14:13:06.120837  3664 SampleEntryController.cpp:13] Sample entry controller created
********* 14:13:06.120837  3664 DeviceManager.cpp:638] Registering device controller: sampleEntry
********* 14:13:06.120837  3664 DeviceManager.cpp:640] Device controller registered successfully: sampleEntry
********* 14:13:06.120837  3664 DeviceManager.cpp:641] Total registered controllers: 15
********* 14:13:06.120837  3664 DeviceManager.cpp:563] Sample entry controller initialized
********* 14:13:06.121838  3664 SampleExitController.cpp:12] Sample exit controller created
********* 14:13:06.121838  3664 DeviceManager.cpp:638] Registering device controller: sampleExit
********* 14:13:06.121838  3664 DeviceManager.cpp:640] Device controller registered successfully: sampleExit
********* 14:13:06.121838  3664 DeviceManager.cpp:641] Total registered controllers: 16
********* 14:13:06.121838  3664 DeviceManager.cpp:568] Sample exit controller initialized
********* 14:13:06.121838  3664 RepoController.cpp:12] Repo controller created
********* 14:13:06.121838  3664 DeviceManager.cpp:638] Registering device controller: repo
********* 14:13:06.121838  3664 DeviceManager.cpp:640] Device controller registered successfully: repo
********* 14:13:06.121838  3664 DeviceManager.cpp:641] Total registered controllers: 17
********* 14:13:06.121838  3664 DeviceManager.cpp:573] Repo controller initialized
********* 14:13:06.121838  3664 DosingController.cpp:12] Dosing controller created
********* 14:13:06.121838  3664 DeviceManager.cpp:638] Registering device controller: dosing
********* 14:13:06.121838  3664 DeviceManager.cpp:640] Device controller registered successfully: dosing
********* 14:13:06.122838  3664 DeviceManager.cpp:641] Total registered controllers: 18
********* 14:13:06.122838  3664 DeviceManager.cpp:578] Dosing controller initialized
********* 14:13:06.122838  3664 VolumeController.cpp:12] Volume controller created
********* 14:13:06.122838  3664 DeviceManager.cpp:638] Registering device controller: volume
********* 14:13:06.122838  3664 DeviceManager.cpp:640] Device controller registered successfully: volume
********* 14:13:06.122838  3664 DeviceManager.cpp:641] Total registered controllers: 19
********* 14:13:06.122838  3664 DeviceManager.cpp:583] Volume controller initialized
********* 14:13:06.122838  3664 FilterController.cpp:12] Filter controller created
********* 14:13:06.122838  3664 DeviceManager.cpp:638] Registering device controller: filter
********* 14:13:06.122838  3664 DeviceManager.cpp:640] Device controller registered successfully: filter
********* 14:13:06.122838  3664 DeviceManager.cpp:641] Total registered controllers: 20
********* 14:13:06.122838  3664 DeviceManager.cpp:588] Filter controller initialized
********* 14:13:06.122838  3664 StirController.cpp:11] Stir controller created
********* 14:13:06.122838  3664 DeviceManager.cpp:638] Registering device controller: stir
********* 14:13:06.122838  3664 DeviceManager.cpp:640] Device controller registered successfully: stir
********* 14:13:06.122838  3664 DeviceManager.cpp:641] Total registered controllers: 21
********* 14:13:06.122838  3664 DeviceManager.cpp:593] Stir controller initialized
********* 14:13:06.122838  3664 ICPEntryController.cpp:11] ICP entry controller created
********* 14:13:06.123838  3664 DeviceManager.cpp:638] Registering device controller: icpEntry
********* 14:13:06.123838  3664 DeviceManager.cpp:640] Device controller registered successfully: icpEntry
********* 14:13:06.123838  3664 DeviceManager.cpp:641] Total registered controllers: 22
********* 14:13:06.123838  3664 DeviceManager.cpp:598] ICP entry controller initialized
********* 14:13:06.123838  3664 ShakerController.cpp:11] Shaker controller created
********* 14:13:06.123838  3664 DeviceManager.cpp:638] Registering device controller: shaker
********* 14:13:06.123838  3664 DeviceManager.cpp:640] Device controller registered successfully: shaker
********* 14:13:06.123838  3664 DeviceManager.cpp:641] Total registered controllers: 23
********* 14:13:06.123838  3664 DeviceManager.cpp:603] Shaker controller initialized
********* 14:13:06.123838  3664 FrameController.cpp:11] Frame controller created
********* 14:13:06.123838  3664 DeviceManager.cpp:638] Registering device controller: frame
********* 14:13:06.123838  3664 DeviceManager.cpp:640] Device controller registered successfully: frame
********* 14:13:06.123838  3664 DeviceManager.cpp:641] Total registered controllers: 24
********* 14:13:06.123838  3664 DeviceManager.cpp:608] Frame controller initialized
********* 14:13:06.123838  3664 PouringController.cpp:19] Pouring controller created
********* 14:13:06.123838  3664 PouringController.cpp:319] Balance or axis driver not provided
********* 14:13:06.123838  3664 PouringController.cpp:23] Failed to initialize pouring algorithm
********* 14:13:06.124838  3664 DeviceManager.cpp:638] Registering device controller: pouring
********* 14:13:06.124838  3664 DeviceManager.cpp:640] Device controller registered successfully: pouring
********* 14:13:06.124838  3664 DeviceManager.cpp:641] Total registered controllers: 25
********* 14:13:06.124838  3664 DeviceManager.cpp:628] Pouring controller initialized
********* 14:13:06.124838  3664 DeviceManager.cpp:99] Device manager initialized successfully
********* 14:13:06.124838  3664 AnalysisRobotApp.cpp:257] Device manager initialized successfully
********* 14:13:06.124838  3664 AnalysisRobotApp.cpp:262] Initializing REST server
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:24] Initializing REST interface driver
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:25] Config - Host: 0.0.0.0, Port: 8080
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:26] Config - Max connections: 100
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:27] Config - Thread pool size: 4
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:32] Creating HTTP server
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:41] Initializing default routes
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:141] Initializing default routes
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:179] Setting up device routes
********* 14:13:06.124838  3664 RestInterfaceDriver.cpp:185] Setting up device routes
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:352] Device routes setup completed
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:181] Default routes initialized successfully
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:44] REST interface driver initialized successfully
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:281] REST server initialized successfully
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:286] Registering device controllers
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:289] Retrieved 25 device controllers from device manager
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:297] Registering device controller: balance1
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:85] Registering device controller: balance1
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: balance1
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 1
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: balance1
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:297] Registering device controller: balance2
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:85] Registering device controller: balance2
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: balance2
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 2
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: balance2
********* 14:13:06.125837  3664 AnalysisRobotApp.cpp:297] Registering device controller: dosing
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:85] Registering device controller: dosing
********* 14:13:06.125837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: dosing
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 3
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: dosing
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:297] Registering device controller: filter
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:85] Registering device controller: filter
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: filter
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 4
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: filter
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:297] Registering device controller: frame
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:85] Registering device controller: frame
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: frame
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 5
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: frame
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater1
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater1
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater1
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 6
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater1
********* 14:13:06.126837  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater10
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater10
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater10
********* 14:13:06.126837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 7
********* 14:13:06.127837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater10
********* 14:13:06.127837  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater11
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater11
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater11
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 8
********* 14:13:06.127837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater11
********* 14:13:06.127837  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater2
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater2
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater2
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 9
********* 14:13:06.127837  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater2
********* 14:13:06.127837  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater3
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater3
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater3
********* 14:13:06.127837  3664 RestInterfaceDriver.cpp:88] Total registered devices: 10
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater3
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater4
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater4
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater4
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:88] Total registered devices: 11
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater4
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater5
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater5
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater5
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:88] Total registered devices: 12
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater5
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater6
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater6
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater6
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:88] Total registered devices: 13
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater6
********* 14:13:06.128510  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater7
********* 14:13:06.128510  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater7
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater7
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 14
********* 14:13:06.129515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater7
********* 14:13:06.129515  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater8
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater8
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater8
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 15
********* 14:13:06.129515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater8
********* 14:13:06.129515  3664 AnalysisRobotApp.cpp:297] Registering device controller: heater9
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:85] Registering device controller: heater9
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: heater9
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 16
********* 14:13:06.129515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: heater9
********* 14:13:06.129515  3664 AnalysisRobotApp.cpp:297] Registering device controller: icpEntry
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:85] Registering device controller: icpEntry
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: icpEntry
********* 14:13:06.129515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 17
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: icpEntry
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:297] Registering device controller: moistureBalance
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:85] Registering device controller: moistureBalance
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: moistureBalance
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 18
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: moistureBalance
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:297] Registering device controller: pouring
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:85] Registering device controller: pouring
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: pouring
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 19
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: pouring
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:297] Registering device controller: repo
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:85] Registering device controller: repo
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: repo
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 20
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: repo
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:297] Registering device controller: sampleEntry
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:85] Registering device controller: sampleEntry
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: sampleEntry
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 21
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: sampleEntry
********* 14:13:06.130515  3664 AnalysisRobotApp.cpp:297] Registering device controller: sampleExit
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:85] Registering device controller: sampleExit
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: sampleExit
********* 14:13:06.130515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 22
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: sampleExit
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:297] Registering device controller: shaker
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:85] Registering device controller: shaker
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: shaker
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 23
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: shaker
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:297] Registering device controller: stir
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:85] Registering device controller: stir
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: stir
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 24
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: stir
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:297] Registering device controller: volume
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:85] Registering device controller: volume
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:87] Device controller registered successfully: volume
********* 14:13:06.131515  3664 RestInterfaceDriver.cpp:88] Total registered devices: 25
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:299] Successfully registered device controller: volume
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:302] All 25 device controllers registered successfully
********* 14:13:06.131515  3664 AnalysisRobotApp.cpp:52] Analysis Robot App initialized successfully
********* 14:13:06.132515  3664 main.cpp:297] Application initialized successfully
********* 14:13:06.132515  3664 main.cpp:135] Performing initial log cleanup...
********* 14:13:06.132515  3664 main.cpp:92] Log cleanup completed: no old files to delete
********* 14:13:06.132515  3664 main.cpp:142] Log cleanup thread started successfully
********* 14:13:06.133517  3664 AnalysisRobotApp.cpp:72] Starting Analysis Robot App
********* 14:13:06.133517  3664 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:13:06.133517  3664 DeviceManager.cpp:145] Attempting to connect balance device 0
********* 14:13:06.133517  3664 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:13:06.133517  3664 DeviceManager.cpp:147] Failed to connect balance device 0
********* 14:13:06.133517 24292 main.cpp:105] Log cleanup thread started, checking every 24 hours
********* 14:13:06.133517  3664 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:13:06.133517  3664 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:13:06.133517  3664 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:13:06.133517  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:13:11.370220  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:13:11.370220  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:13:11.370220  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 1
********* 14:13:11.370220  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM4
********* 14:13:11.370220  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 1
********* 14:13:11.371223  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 2
********* 14:13:11.371223  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM5
********* 14:13:11.371223  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 2
********* 14:13:11.371223  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:13:11.371223  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:13:11.371223  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:13:11.371223  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:13:11.371223  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:13:11.371223  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:13:11.371223  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:13:11.371223  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:13:11.371223  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:13:11.371223  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:13:11.371223  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:13:11.372224  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:13:11.372224  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:13:11.372224  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:13:11.372224  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:13:11.372224  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:13:11.372224  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:13:11.372224  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:13:11.372224  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:13:11.372224  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:13:11.372224  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:13:11.372224  3664 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:13:11.372224  3664 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:13:11.373224  3664 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:13:11.373224  3664 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:13:11.373224  3664 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:13:11.373224  3664 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:13:11.373224  3664 DeviceManager.cpp:221] Device connection summary: 0 already connected, 0 newly connected, 14 failed
********* 14:13:11.373224  3664 DeviceManager.cpp:229] Some devices failed to connect
********* 14:13:11.373224  3664 AnalysisRobotApp.cpp:77] Some devices failed to connect, continuing anyway
********* 14:13:11.373224  3664 DeviceManager.cpp:112] Starting all devices
********* 14:13:11.373224  3664 DeviceManager.cpp:117] All devices started
********* 14:13:11.373224  3664 RestInterfaceDriver.cpp:62] REST server started on 0.0.0.0:8080
********* 14:13:11.373224  3664 AnalysisRobotApp.cpp:96] Analysis Robot App started successfully
********* 14:13:11.374225  8568 RestInterfaceDriver.cpp:120] Server loop started
********* 14:13:11.374225  3664 AnalysisRobotApp.cpp:144] Analysis Robot App is running. Press Ctrl+C to stop.
********* 14:13:11.374225 23976 AnalysisRobotApp.cpp:307] Monitor thread started
********* 14:13:11.374225  8568 RestInterfaceDriver.cpp:121] Starting HTTP server on 0.0.0.0:8080
********* 14:13:11.374225  8568 RestInterfaceDriver.cpp:124] Server object exists, calling listen()
********* 14:13:21.440608 23976 DeviceManager.cpp:332] Device balance1 not connected, status: DISCONNECTED
********* 14:13:21.440608 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater2 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater3 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:13:21.441145 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:13:21.442148 23976 DeviceManager.cpp:341] Device connection status: 1/25 connected
********* 14:13:21.442148 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:13:21.442148 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 70 seconds ago)
********* 14:13:21.442148 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:13:21.443148 23976 DeviceManager.cpp:145] Attempting to connect balance device 0
********* 14:13:21.444149 23976 DeviceManager.cpp:151] Balance device 0 connected successfully
********* 14:13:21.444149 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:13:21.444149 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:13:21.444149 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:13:21.444149 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:13:26.703787 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:13:26.703787 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:13:26.703787 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 1
********* 14:13:26.704787 23976 HeatingMagneticStirrerDriver.cpp:99] Serial port COM4 opened successfully at 000001C9822F5BC0
********* 14:13:26.704787 23976 DeviceManager.cpp:172] Stirrer heater device 1 connected successfully
********* 14:13:26.704787 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 2
********* 14:13:26.772112 23976 HeatingMagneticStirrerDriver.cpp:99] Serial port COM5 opened successfully at 000001C9822F5980
********* 14:13:26.772112 23976 DeviceManager.cpp:172] Stirrer heater device 2 connected successfully
********* 14:13:26.772112 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:13:26.772112 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:13:26.772112 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:13:26.773113 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:13:26.773113 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:13:26.773113 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:13:26.773113 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:13:26.773113 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:13:26.773113 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:13:26.773113 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:13:26.773113 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:13:26.774111 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:13:26.774111 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:13:26.774111 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:13:26.774111 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:13:26.774111 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:13:26.774111 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:13:26.774111 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:13:26.774111 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:13:26.774111 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:13:26.774111 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:13:26.775112 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:13:26.775112 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:13:26.775112 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:13:26.775112 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:13:26.775112 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:13:26.775112 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:13:26.775112 23976 DeviceManager.cpp:221] Device connection summary: 0 already connected, 3 newly connected, 11 failed
********* 14:13:26.775112 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:13:31.547353 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:13:31.547353 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:13:31.547353 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:13:31.547353 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:13:31.547353 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:13:31.547353 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:13:31.547353 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:13:35.327795 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:13:35.327795 23976 HeatingMagneticStirrerDriver.cpp:494] Read failure 1/5: Failed to send read request after 3 retries
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:13:35.693502 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:13:35.694504 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:13:35.694504 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:13:35.694504 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:13:35.694504 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:13:35.694504 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:13:35.694504 23976 DeviceManager.cpp:341] Device connection status: 4/25 connected
********* 14:13:35.694504 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:13:35.694504 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:13:41.557904 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:13:41.557904 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:13:41.557904 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:13:41.557904 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:13:41.557904 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:13:41.558904 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:13:41.558904 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:13:45.336024 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:13:45.336024 23976 HeatingMagneticStirrerDriver.cpp:494] Read failure 2/5: Failed to send read request after 3 retries
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:13:45.702033 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:13:45.703034 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:13:45.703034 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:13:45.703034 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:13:45.703034 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:13:45.703034 23976 DeviceManager.cpp:341] Device connection status: 4/25 connected
********* 14:13:45.703034 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:13:45.703034 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:13:51.569869 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:13:51.569869 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:13:51.569869 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:13:51.569869 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:13:51.569869 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:13:51.569869 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:13:51.570869 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:13:55.344336 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:13:55.344336 23976 HeatingMagneticStirrerDriver.cpp:494] Read failure 3/5: Failed to send read request after 3 retries
********* 14:13:55.709345 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:13:55.709345 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:13:55.709345 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:13:55.709345 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:13:55.709345 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:13:55.709345 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:13:55.710346 23976 DeviceManager.cpp:341] Device connection status: 4/25 connected
********* 14:13:55.710346 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:13:55.710346 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:13:55.711345 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:13:55.711345 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:13:55.711345 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:13:55.711345 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:13:55.711345 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:13:55.711345 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:14:00.944837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:14:00.944837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:14:00.944837 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:14:00.945837 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:14:00.945837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:14:00.945837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:14:00.945837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:14:00.945837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:14:00.945837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:14:00.945837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:14:00.946837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:14:00.946837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:14:00.946837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:14:00.946837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:14:00.946837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:14:00.946837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:14:00.946837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:14:00.946837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:14:00.946837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:14:00.946837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:14:00.947837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:14:00.947837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:14:00.947837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:14:00.947837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:14:00.947837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:14:00.947837 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:14:00.947837 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:14:00.947837 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:14:00.947837 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:14:00.948837 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:14:00.948837 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:14:00.948837 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:14:00.948837 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:14:01.604009 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:14:01.604009 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:14:01.604009 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:14:01.604009 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:14:01.604009 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:14:01.604009 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:14:01.604009 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:14:05.388334 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:14:05.388334 23976 HeatingMagneticStirrerDriver.cpp:494] Read failure 4/5: Failed to send read request after 3 retries
********* 14:14:05.753705 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:14:05.753705 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:14:05.753705 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:14:05.753705 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:14:05.753705 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:14:05.753705 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:14:05.754706 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:14:05.754706 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:14:05.754706 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:14:05.754706 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:14:05.754706 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:14:05.754706 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:14:05.755208 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:14:05.755208 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:14:05.755208 23976 DeviceManager.cpp:341] Device connection status: 4/25 connected
********* 14:14:05.755208 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:14:05.755208 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:14:11.625454 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:14:11.625454 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:14:11.625454 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:14:11.625454 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:14:11.625454 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:14:11.625454 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:14:11.625454 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:14:15.410605 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:14:15.410605 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:14:15.787040 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:14:15.788038 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:14:15.788038 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:14:15.788038 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:14:15.788038 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:14:15.788038 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:14:15.788038 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:14:15.788038 23976 DeviceManager.cpp:341] Device connection status: 4/25 connected
********* 14:14:15.788038 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:14:15.788038 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:14:21.643363 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:14:21.643363 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:14:21.643363 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:14:21.643363 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:14:21.643363 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:14:21.643363 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:14:21.643363 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:14:25.418061 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:14:25.418061 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:14:25.418061 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:14:25.783295 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:14:25.784297 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:14:25.784297 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:14:25.784297 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:14:25.784297 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:14:25.784297 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:14:25.784297 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:14:25.784297 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:14:25.784297 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:14:25.784297 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:14:25.784297 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:14:25.784297 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:14:25.784297 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:14:25.784297 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:14:31.039840 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:14:31.039840 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:14:31.039840 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:14:31.040750 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:14:31.040750 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:14:31.040750 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:14:31.040750 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:14:31.040750 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:14:31.040750 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:14:31.040750 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:14:31.040750 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:14:31.040750 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:14:31.040750 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:14:31.040750 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:14:31.040750 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:14:31.040750 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:14:31.040750 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:14:31.040750 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:14:31.041747 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:14:31.041747 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:14:31.041747 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:14:31.041747 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:14:31.041747 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:14:31.041747 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:14:31.041747 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:14:31.041747 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:14:31.041747 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:14:31.041747 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:14:31.041747 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:14:31.041747 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:14:31.041747 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:14:31.041747 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:14:31.041747 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:14:31.710186 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:14:31.710186 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:14:31.710186 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:14:31.710186 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:14:31.710186 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:14:31.710186 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:14:31.711189 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:14:35.492350 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:14:35.492350 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:14:35.492350 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:14:35.857066 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:14:35.858067 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:14:35.858067 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:14:35.858067 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:14:35.858067 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:14:35.858067 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:14:35.858067 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:14:35.858067 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:14:41.730074 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:14:41.730074 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:14:41.730074 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:14:41.730074 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:14:41.730074 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:14:41.730074 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:14:41.730074 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:14:45.510394 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:14:45.510394 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:14:45.510394 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:14:45.890978 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:14:45.891979 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:14:45.891979 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:14:45.891979 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:14:45.891979 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:14:45.891979 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:14:45.891979 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:14:45.891979 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:14:45.891979 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:14:45.891979 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:14:51.746402 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:14:51.746402 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:14:51.746402 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:14:51.746402 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:14:51.746402 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:14:51.746402 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:14:51.746402 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:14:55.542230 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:14:55.542230 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:14:55.542230 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:14:55.920653 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:14:55.920653 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:14:55.920653 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:14:55.920653 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:14:55.920653 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:14:55.920653 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:14:55.920653 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:14:55.921653 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:14:55.921653 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:14:55.921653 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:14:55.921653 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:14:55.921653 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:14:55.921653 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:14:55.922653 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:14:55.922653 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:14:55.922653 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:15:01.159191 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:15:01.159191 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:15:01.160194 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:15:01.160194 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:15:01.160194 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:15:01.160194 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:15:01.160194 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:15:01.160194 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:15:01.160194 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:15:01.160194 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:15:01.160194 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:15:01.160194 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:15:01.160194 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:15:01.160194 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:15:01.161192 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:15:01.161192 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:15:01.161192 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:15:01.161192 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:15:01.161192 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:15:01.161192 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:15:01.161192 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:15:01.161192 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:15:01.161192 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:15:01.161192 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:15:01.161192 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:15:01.161192 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:15:01.161192 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:15:01.161192 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:15:01.161192 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:15:01.162192 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:15:01.162192 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:15:01.162192 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:15:01.162192 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:15:01.818348 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:15:01.818348 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:15:01.818348 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:15:01.818348 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:15:01.818348 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:15:01.818348 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:15:01.818348 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:15:05.589533 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:15:05.589533 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:15:05.589533 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:15:05.956265 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:15:05.957265 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:15:05.957265 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:15:05.957265 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:15:05.957265 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:15:05.957265 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:15:05.957265 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:15:05.957265 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:15:11.924710 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:15:11.924710 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:15:11.924710 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:15:11.924710 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:15:11.924710 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:15:11.924710 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:15:11.924710 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:15:15.714650 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:15:15.714650 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:15:15.714650 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:15:16.093525 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:15:16.094525 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:15:16.094525 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:15:16.094525 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:15:16.094525 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:15:16.094525 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:15:16.094525 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:15:16.094525 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:15:16.094525 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:15:21.929001 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:15:21.929001 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:15:21.929001 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:15:21.929001 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:15:21.929001 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:15:21.929001 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:15:21.929001 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:15:25.713495 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:15:25.713495 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:15:25.713495 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:15:26.091272 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:15:26.092271 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:15:26.092271 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:15:26.092271 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:15:26.092271 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:15:26.092271 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:15:26.092271 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:15:26.092271 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:15:26.092271 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:15:26.092271 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:15:26.092271 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:15:26.092271 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:15:26.092271 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:15:26.092271 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:15:26.092271 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:15:26.092271 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:15:31.327932 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:15:31.327932 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:15:31.328933 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:15:31.328933 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:15:31.328933 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:15:31.328933 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:15:31.328933 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:15:31.328933 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:15:31.328933 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:15:31.328933 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:15:31.328933 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:15:31.328933 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:15:31.328933 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:15:31.328933 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:15:31.329932 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:15:31.329932 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:15:31.329932 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:15:31.329932 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:15:31.329932 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:15:31.329932 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:15:31.329932 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:15:31.329932 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:15:31.329932 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:15:31.329932 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:15:31.329932 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:15:31.329932 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:15:31.329932 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:15:31.329932 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:15:31.329932 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:15:31.330932 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:15:31.330932 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:15:31.330932 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:15:31.330932 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:15:31.989770 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:15:31.989770 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:15:31.989770 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:15:31.989770 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:15:31.989770 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:15:31.989770 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:15:31.989770 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:15:35.772192 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:15:35.772192 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:15:35.772192 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:15:36.149610 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:15:36.150612 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:15:36.150612 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:15:36.150612 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:15:36.150612 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:15:36.150612 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:15:41.996556 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:15:41.996556 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:15:41.996556 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:15:41.997102 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:15:41.997102 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:15:41.997102 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:15:41.997102 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:15:45.771657 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:15:45.771657 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:15:45.771657 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:15:46.151149 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:15:46.152148 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:15:46.152148 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:15:46.152148 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:15:46.152148 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:15:46.152148 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:15:46.152148 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:15:46.152148 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:15:46.152148 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:15:52.011580 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:15:52.011580 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:15:52.011580 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:15:52.011580 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:15:52.011580 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:15:52.011580 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:15:52.011580 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:15:55.808709 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:15:55.808709 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:15:55.809230 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:15:56.188752 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:15:56.189754 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:15:56.189754 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:15:56.189754 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:15:56.189754 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:15:56.189754 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:15:56.189754 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:15:56.189754 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:15:56.189754 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:15:56.189754 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:15:56.190256 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:15:56.190256 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:16:01.425375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:16:01.425375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:16:01.425375 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:16:01.425375 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:16:01.425375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:16:01.425375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:16:01.425375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:16:01.425375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:16:01.425375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:16:01.425375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:16:01.425375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:16:01.425375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:16:01.426375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:16:01.426375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:16:01.426375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:16:01.426375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:16:01.426375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:16:01.426375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:16:01.426375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:16:01.426375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:16:01.426375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:16:01.426375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:16:01.426375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:16:01.426375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:16:01.426375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:16:01.426375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:16:01.427376 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:16:01.427376 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:16:01.427376 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:16:01.427376 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:16:01.427376 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:16:01.427376 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:16:01.427376 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:16:02.091403 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:16:02.091403 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:16:02.091403 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:16:02.091403 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:16:02.091403 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:16:02.091403 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:16:02.091403 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:16:05.874189 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:16:05.874189 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:16:05.874189 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:16:06.237973 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:16:06.238973 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:16:06.238973 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:16:06.238973 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:16:06.238973 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:16:06.238973 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:16:06.238973 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:16:06.238973 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:16:06.238973 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:16:12.095876 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:16:12.095876 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:16:12.095876 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:16:12.095876 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:16:12.095876 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:16:12.095876 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:16:12.096877 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:16:15.888693 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:16:15.888693 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:16:15.888693 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:16:16.253942 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:16:16.253942 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:16:16.253942 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:16:16.254475 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:16:16.254475 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:16:16.254475 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:16:22.106945 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:16:22.106945 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:16:22.106945 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:16:22.106945 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:16:22.106945 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:16:22.106945 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:16:22.106945 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:16:25.881181 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:16:25.881181 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:16:25.881181 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:16:26.261101 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:16:26.262101 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:16:26.262101 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:16:26.262101 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:16:26.262101 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:16:26.262101 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:16:26.262101 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:16:26.262101 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:16:26.262101 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:16:26.262101 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:16:26.262101 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:16:26.262101 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:16:26.262101 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:16:26.262101 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:16:26.262101 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:16:26.262101 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:16:31.497714 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:16:31.497714 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:16:31.497714 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:16:31.497714 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:16:31.498715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:16:31.498715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:16:31.498715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:16:31.498715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:16:31.498715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:16:31.498715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:16:31.498715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:16:31.498715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:16:31.498715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:16:31.498715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:16:31.498715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:16:31.498715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:16:31.498715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:16:31.498715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:16:31.499715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:16:31.499715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:16:31.499715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:16:31.499715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:16:31.499715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:16:31.499715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:16:31.499715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:16:31.499715 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:16:31.499715 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:16:31.499715 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:16:31.499715 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:16:31.499715 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:16:31.499715 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:16:31.499715 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:16:31.499715 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:16:32.167018 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:16:32.167018 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:16:32.167018 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:16:32.167018 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:16:32.167018 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:16:32.167018 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:16:32.167018 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:16:35.944880 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:16:35.944880 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:16:35.944880 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:16:36.309365 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:16:36.309365 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:16:36.309916 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:16:36.309916 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:16:36.309916 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:16:36.310433 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:16:36.310433 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:16:36.310433 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:16:36.310433 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:16:36.310433 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:16:36.310946 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:16:36.310946 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:16:36.310946 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:16:36.310946 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:16:36.310946 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:16:36.310946 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:16:36.310946 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:16:42.274547 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:16:42.274547 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:16:42.274547 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:16:42.274547 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:16:42.274547 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:16:42.274547 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:16:42.274547 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:16:46.057910 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:16:46.057910 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:16:46.057910 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:16:46.433909 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:16:46.434909 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:16:46.434909 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:16:46.434909 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:16:46.434909 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:16:46.434909 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:16:46.434909 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:16:46.434909 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:16:46.434909 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:16:46.434909 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:16:52.285645 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:16:52.285645 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:16:52.285645 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:16:52.285645 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:16:52.285645 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:16:52.285645 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:16:52.285645 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:16:56.060701 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:16:56.060701 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:16:56.060701 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:16:56.424484 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:16:56.424484 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:16:56.424986 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:16:56.425499 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:16:56.425499 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:16:56.425499 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:16:56.425499 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:16:56.425499 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:16:56.425499 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:16:56.425499 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:16:56.425499 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:16:56.425499 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:16:56.426010 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:16:56.426010 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:16:56.426010 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:16:56.426010 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:17:01.659099 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:17:01.659099 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:17:01.659099 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:17:01.659099 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:17:01.659099 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:17:01.660100 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:17:01.660100 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:17:01.660100 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:17:01.660100 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:17:01.660100 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:17:01.660100 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:17:01.660100 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:17:01.660100 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:17:01.660100 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:17:01.660100 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:17:01.660100 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:17:01.660100 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:17:01.661099 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:17:01.661099 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:17:01.661099 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:17:01.661099 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:17:01.661099 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:17:01.661099 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:17:01.661099 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:17:01.661099 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:17:01.661099 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:17:01.661099 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:17:01.661099 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:17:01.661099 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:17:01.661099 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:17:01.662099 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:17:01.662099 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:17:01.662099 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:17:02.329763 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:17:02.329763 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:17:02.329763 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:17:02.329763 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:17:02.329763 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:17:02.329763 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:17:02.329763 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:17:06.111217 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:17:06.111217 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:17:06.111217 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:17:06.489821 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:17:06.490821 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:17:06.490821 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:17:06.490821 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:17:06.490821 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:17:06.490821 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:17:06.490821 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:17:06.490821 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:17:12.347517 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:17:12.347517 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:17:12.347517 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:17:12.347517 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:17:12.347517 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:17:12.347517 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:17:12.347517 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:17:16.133553 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:17:16.133553 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:17:16.133553 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:17:16.513161 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:17:16.514159 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:17:16.514159 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:17:16.514159 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:17:16.514159 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:17:16.514159 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:17:22.372505 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:17:22.372505 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:17:22.372505 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:17:22.372505 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:17:22.372505 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:17:22.372505 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:17:22.372505 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:17:26.160753 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:17:26.160753 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:17:26.160753 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:17:26.526414 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:17:26.527415 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:17:26.527415 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:17:26.527415 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:17:26.527415 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:17:26.527415 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:17:26.527415 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:17:26.527415 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:17:26.527415 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:17:26.527415 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:17:26.527415 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:17:26.527415 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:17:26.527415 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:17:26.527415 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:17:26.527415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:17:31.762288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:17:31.762288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:17:31.762288 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:17:31.762288 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:17:31.762288 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:17:31.762288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:17:31.762288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:17:31.762288 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:17:31.762288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:17:31.762288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:17:31.763288 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:17:31.763288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:17:31.763288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:17:31.763288 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:17:31.763288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:17:31.763288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:17:31.763288 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:17:31.763288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:17:31.763288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:17:31.763288 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:17:31.763288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:17:31.763288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:17:31.763288 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:17:31.763288 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:17:31.763288 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:17:31.764287 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:17:31.764287 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:17:31.764287 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:17:31.764287 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:17:31.764287 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:17:31.764287 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:17:31.764287 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:17:31.764287 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:17:32.429579 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:17:32.429579 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:17:32.429579 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:17:32.429579 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:17:32.429579 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:17:32.429579 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:17:32.429579 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:17:36.205626 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:17:36.205626 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:17:36.205626 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:17:36.571995 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:17:36.572995 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:17:36.572995 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:17:36.572995 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:17:36.572995 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:17:36.572995 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:17:36.572995 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:17:36.572995 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:17:42.437424 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:17:42.437424 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:17:42.437424 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:17:42.437424 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:17:42.437424 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:17:42.437424 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:17:42.437424 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:17:46.206586 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:17:46.206586 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:17:46.206586 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:17:46.586148 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:17:46.586148 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:17:46.586148 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:17:46.586148 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:17:46.586148 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:17:46.586148 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:17:46.586148 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:17:46.587148 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:17:46.587148 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:17:46.587148 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:17:46.587148 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:17:46.587148 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:17:46.587148 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:17:46.587652 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:17:46.587652 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:17:46.587652 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:17:46.587652 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:17:52.534675 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:17:52.534675 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:17:52.534675 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:17:52.534675 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:17:52.534675 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:17:52.534675 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:17:52.534675 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:17:56.311033 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:17:56.311033 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:17:56.311033 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:17:56.689538 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:17:56.690538 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:17:56.690538 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:17:56.690538 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:17:56.690538 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:17:56.690538 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:17:56.690538 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:17:56.690538 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:17:56.690538 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:17:56.690538 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:17:56.690538 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:17:56.690538 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:17:56.690538 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:17:56.690538 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:18:01.815752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:18:01.815752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:18:01.816752 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:18:01.816752 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:18:01.816752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:18:01.816752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:18:01.816752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:18:01.816752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:18:01.816752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:18:01.816752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:18:01.816752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:18:01.816752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:18:01.816752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:18:01.816752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:18:01.816752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:18:01.817752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:18:01.817752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:18:01.817752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:18:01.817752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:18:01.817752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:18:01.817752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:18:01.817752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:18:01.817752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:18:01.817752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:18:01.817752 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:18:01.817752 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:18:01.817752 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:18:01.818753 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:18:01.818753 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:18:01.818753 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:18:01.818753 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:18:01.818753 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:18:01.818753 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:18:02.584913 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:18:02.584913 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:18:02.584913 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:18:02.584913 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:18:02.584913 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:18:02.584913 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:18:02.584913 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:18:06.363986 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:18:06.363986 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:18:06.363986 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:18:06.742408 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:18:06.743407 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:18:06.743407 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:18:06.743407 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:18:06.743407 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:18:06.743407 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:18:06.743407 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:18:06.743407 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:18:06.743407 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:18:12.612706 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:18:12.612706 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:18:12.612706 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:18:12.612706 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:18:12.612706 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:18:12.612706 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:18:12.612706 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:18:16.392707 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:18:16.392707 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:18:16.392707 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:18:16.755805 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:18:16.755805 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:18:16.755805 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:18:16.755805 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:18:16.755805 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:18:16.755805 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:18:16.756806 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:18:16.757807 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:18:16.757807 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:18:16.757807 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:18:22.701172 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:18:22.701172 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:18:22.701172 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:18:22.701172 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:18:22.701172 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:18:22.701172 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:18:22.701172 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:18:26.483083 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:18:26.483083 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:18:26.483083 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:18:26.861142 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:18:26.862144 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:18:26.862144 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:18:26.862144 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:18:26.862144 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:18:26.862144 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:18:26.862144 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:18:26.862144 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:18:26.862144 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:18:26.862144 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:18:26.862144 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:18:26.862144 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:18:32.098850 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:18:32.098850 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:18:32.098850 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:18:32.098850 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:18:32.098850 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:18:32.099851 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:18:32.099851 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:18:32.099851 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:18:32.099851 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:18:32.099851 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:18:32.099851 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:18:32.099851 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:18:32.099851 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:18:32.099851 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:18:32.099851 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:18:32.099851 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:18:32.099851 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:18:32.099851 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:18:32.099851 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:18:32.099851 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:18:32.099851 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:18:32.100850 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:18:32.100850 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:18:32.100850 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:18:32.100850 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:18:32.100850 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:18:32.100850 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:18:32.100850 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:18:32.100850 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:18:32.100850 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:18:32.101850 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:18:32.101850 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:18:32.101850 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:18:32.763150 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:18:32.763150 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:18:32.763150 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:18:32.763150 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:18:32.763150 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:18:32.763150 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:18:32.763150 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:18:36.548187 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:18:36.548187 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:18:36.548187 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:18:36.925835 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:18:36.926836 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:18:36.926836 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:18:36.926836 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:18:36.926836 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:18:36.926836 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:18:36.926836 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:18:36.926836 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:18:42.803419 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:18:42.803419 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:18:42.803419 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:18:42.803419 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:18:42.803419 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:18:42.803419 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:18:42.803419 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:18:46.592526 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:18:46.592526 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:18:46.592526 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:18:46.972273 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:18:46.972273 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:18:46.972273 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:18:46.972273 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:18:46.972273 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:18:46.972273 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:18:46.972273 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:18:46.973273 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:18:46.973273 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:18:46.973273 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:18:52.819195 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:18:52.819195 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:18:52.819195 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:18:52.819195 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:18:52.819195 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:18:52.819195 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:18:52.819195 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:18:56.601349 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:18:56.601349 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:18:56.601349 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:18:56.978885 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:18:56.978885 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:18:56.978885 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:18:56.978885 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:18:56.978885 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:18:56.978885 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:18:56.979887 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:18:56.979887 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:18:56.980886 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:18:56.980886 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:18:56.980886 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:18:56.980886 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:18:56.980886 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:18:56.980886 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:18:56.980886 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:19:02.214663 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:19:02.215662 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:19:02.215662 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:19:02.215662 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:19:02.215662 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:19:02.215662 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:19:02.215662 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:19:02.215662 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:19:02.215662 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:19:02.215662 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:19:02.215662 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:19:02.215662 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:19:02.215662 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:19:02.216663 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:19:02.216663 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:19:02.216663 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:19:02.216663 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:19:02.216663 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:19:02.216663 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:19:02.216663 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:19:02.216663 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:19:02.216663 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:19:02.216663 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:19:02.216663 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:19:02.216663 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:19:02.217664 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:19:02.217664 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:19:02.217664 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:19:02.217664 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:19:02.217664 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:19:02.217664 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:19:02.217664 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:19:02.217664 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:19:02.874589 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:19:02.874589 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:19:02.874589 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:19:02.874589 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:19:02.874589 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:19:02.874589 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:19:02.874589 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:19:06.655951 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:19:06.655951 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:19:06.655951 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:19:07.035459 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:19:07.036459 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:19:07.036459 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:19:07.036459 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:19:07.036459 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:19:07.036459 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:19:07.036459 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:19:07.036459 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:19:07.036459 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:19:12.882997 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:19:12.882997 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:19:12.882997 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:19:12.882997 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:19:12.882997 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:19:12.882997 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:19:12.882997 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:19:16.661060 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:19:16.661060 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:19:16.661060 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:19:17.040066 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:19:17.041066 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:19:17.041066 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:19:17.041066 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:19:17.041066 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:19:17.041066 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:19:17.041066 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:19:17.041066 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:19:17.041066 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:19:17.041066 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:19:22.898068 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:19:22.898068 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:19:22.898068 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:19:22.898068 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:19:22.898068 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:19:22.898068 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:19:22.898068 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:19:26.699414 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:19:26.699414 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:19:26.699414 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:19:27.077816 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:19:27.078817 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:19:27.078817 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:19:27.078817 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:19:27.078817 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:19:27.078817 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:19:27.078817 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:19:27.078817 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:19:27.078817 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:19:27.078817 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:19:27.078817 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:19:27.078817 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:19:27.078817 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:19:27.078817 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:19:32.320616 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:19:32.320616 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:19:32.320616 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:19:32.320616 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:19:32.320616 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:19:32.321617 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:19:32.321617 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:19:32.321617 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:19:32.321617 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:19:32.321617 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:19:32.321617 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:19:32.321617 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:19:32.321617 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:19:32.321617 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:19:32.322618 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:19:32.322618 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:19:32.322618 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:19:32.322618 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:19:32.322618 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:19:32.322618 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:19:32.322618 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:19:32.322618 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:19:32.322618 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:19:32.322618 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:19:32.322618 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:19:32.322618 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:19:32.322618 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:19:32.322618 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:19:32.322618 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:19:32.323616 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:19:32.323616 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:19:32.323616 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:19:32.323616 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:19:32.979460 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:19:32.979460 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:19:32.979460 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:19:32.979460 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:19:32.979460 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:19:32.979460 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:19:32.979460 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:19:36.771337 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:19:36.771337 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:19:36.771337 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:19:37.146804 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:19:37.147805 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:19:37.147805 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:19:37.147805 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:19:37.147805 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:19:37.147805 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:19:37.147805 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:19:43.001631 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:19:43.001631 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:19:43.001631 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:19:43.001631 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:19:43.001631 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:19:43.001631 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:19:43.001631 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:19:46.768738 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:19:46.768738 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:19:46.768738 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:19:47.146374 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:19:47.146374 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:19:47.146374 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:19:47.146374 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:19:47.146374 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:19:47.146374 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:19:47.146374 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:19:47.147375 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:19:47.147375 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:19:47.148376 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:19:53.102900 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:19:53.102900 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:19:53.102900 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:19:53.102900 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:19:53.102900 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:19:53.102900 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:19:53.102900 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:19:56.889273 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:19:56.889273 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:19:56.889273 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:19:57.266737 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:19:57.267737 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:19:57.267737 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:19:57.267737 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:19:57.267737 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:19:57.267737 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:19:57.267737 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:19:57.267737 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:19:57.267737 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:19:57.267737 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:19:57.267737 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:19:57.267737 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:19:57.267737 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:19:57.267737 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:20:02.503371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:20:02.503371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:20:02.503371 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:20:02.503371 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:20:02.503371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:20:02.503371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:20:02.503371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:20:02.503371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:20:02.503371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:20:02.503371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:20:02.504371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:20:02.504371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:20:02.504371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:20:02.504371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:20:02.504371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:20:02.504371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:20:02.504371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:20:02.504371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:20:02.504371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:20:02.504371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:20:02.504371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:20:02.504371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:20:02.504371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:20:02.504371 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:20:02.504371 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:20:02.504371 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:20:02.505373 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:20:02.505373 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:20:02.505373 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:20:02.505373 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:20:02.505373 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:20:02.505373 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:20:02.505373 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:20:03.167573 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:20:03.167573 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:20:03.167573 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:20:03.167573 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:20:03.167573 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:20:03.167573 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:20:03.167573 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:20:06.957460 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:20:06.957460 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:20:06.957460 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:20:07.333510 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:20:07.334512 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:20:07.334512 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:20:07.334512 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:20:07.334512 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:20:07.334512 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:20:07.334512 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:20:07.334512 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:20:07.334512 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:20:07.334512 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:20:13.188830 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:20:13.188830 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:20:13.188830 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:20:13.188830 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:20:13.188830 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:20:13.188830 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:20:13.188830 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:20:16.969971 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:20:16.969971 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:20:16.969971 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:20:17.347122 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:20:17.348124 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:20:17.348124 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:20:17.348124 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:20:17.348124 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:20:17.348124 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:20:17.348124 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:20:17.348124 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:20:17.348124 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:20:23.195881 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:20:23.195881 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:20:23.195881 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:20:23.195881 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:20:23.195881 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:20:23.195881 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:20:23.195881 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:20:26.978593 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:20:26.978593 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:20:26.978593 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:20:27.344339 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:20:27.345341 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:20:27.345341 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:20:27.345341 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:20:27.345341 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:20:27.345341 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:20:27.345341 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:20:27.345341 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:20:27.345341 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:20:27.345341 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:20:27.345341 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:20:27.345341 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:20:27.345341 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:20:27.345341 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:20:27.345341 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:20:27.345341 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:20:32.581247 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:20:32.581247 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:20:32.581247 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:20:32.581247 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:20:32.581247 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:20:32.581247 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:20:32.581247 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:20:32.581247 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:20:32.581247 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:20:32.582248 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:20:32.582248 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:20:32.582248 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:20:32.582248 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:20:32.582248 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:20:32.582248 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:20:32.582248 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:20:32.582248 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:20:32.582248 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:20:32.582248 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:20:32.582248 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:20:32.583248 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:20:32.583248 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:20:32.583248 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:20:32.583248 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:20:32.583248 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:20:32.583248 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:20:32.583248 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:20:32.583248 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:20:32.583248 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:20:32.584249 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:20:32.584249 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:20:32.584249 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:20:32.584249 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:20:33.251257 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:20:33.251257 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:20:33.251257 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:20:33.251257 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:20:33.251257 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:20:33.251257 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:20:33.251257 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:20:37.027427 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:20:37.027427 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:20:37.027427 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:20:37.406240 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:20:37.407238 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:20:37.407238 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:20:37.407238 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:20:37.407238 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:20:37.407238 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:20:37.407238 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:20:37.407238 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:20:43.273316 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:20:43.273316 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:20:43.273316 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:20:43.273316 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:20:43.273316 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:20:43.273316 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:20:43.273316 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:20:47.045132 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:20:47.045132 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:20:47.045132 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:20:47.422472 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:20:47.422472 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:20:47.422472 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:20:47.422472 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:20:47.422472 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:20:47.422472 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:20:47.423471 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:20:47.423471 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:20:47.423471 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:20:53.284409 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:20:53.284409 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:20:53.284409 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:20:53.284409 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:20:53.284409 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:20:53.284409 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:20:53.284409 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:20:57.063211 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:20:57.063211 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:20:57.063211 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:20:57.443595 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:20:57.444597 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:20:57.444597 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:20:57.444597 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:20:57.444597 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:20:57.444597 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:20:57.444597 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:20:57.444597 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:20:57.444597 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:20:57.444597 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:20:57.444597 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:20:57.444597 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:20:57.444597 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:21:02.680377 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:21:02.680377 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:21:02.680377 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:21:02.680377 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:21:02.680377 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:21:02.680377 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:21:02.681375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:21:02.681375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:21:02.681375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:21:02.681375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:21:02.681375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:21:02.681375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:21:02.681375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:21:02.681375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:21:02.681375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:21:02.681375 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:21:02.681375 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:21:02.681375 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:21:02.682381 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:21:02.682381 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:21:02.682381 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:21:02.682381 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:21:02.682381 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:21:02.682381 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:21:02.682381 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:21:02.682381 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:21:02.682381 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:21:02.682381 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:21:02.682381 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:21:02.682381 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:21:02.683375 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:21:02.683375 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:21:02.683375 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:21:03.349792 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:21:03.349792 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:21:03.349792 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:21:03.349792 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:21:03.349792 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:21:03.350792 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:21:03.350792 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:21:07.136535 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:21:07.136535 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:21:07.136535 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:21:07.515682 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:21:07.516683 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:21:07.516683 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:21:07.516683 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:21:07.516683 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:21:07.516683 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:21:07.516683 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:21:07.516683 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:21:07.516683 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:21:07.516683 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:21:13.362895 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:21:13.362895 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:21:13.362895 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:21:13.362895 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:21:13.362895 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:21:13.362895 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:21:13.362895 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:21:17.127279 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:21:17.127279 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:21:17.127279 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:21:17.504578 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:21:17.505579 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:21:17.505579 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:21:17.505579 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:21:17.505579 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:21:17.505579 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:21:17.505579 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:21:17.505579 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:21:17.505579 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:21:23.363826 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:21:23.363826 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:21:23.363826 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:21:23.363826 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:21:23.363826 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:21:23.363826 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:21:23.363826 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:21:27.140575 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:21:27.140575 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:21:27.140575 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:21:27.518716 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:21:27.518716 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:21:27.518716 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:21:27.518716 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:21:27.518716 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:21:27.518716 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:21:27.519717 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:21:27.519717 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:21:27.520221 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:21:27.520221 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:21:27.520221 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:21:27.520221 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:21:27.520221 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:21:27.520221 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:21:27.520725 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:21:32.755321 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:21:32.755321 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:21:32.756322 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:21:32.756322 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:21:32.756322 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:21:32.756322 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:21:32.756322 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:21:32.756322 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:21:32.756322 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:21:32.756322 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:21:32.756322 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:21:32.756322 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:21:32.757324 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:21:32.757324 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:21:32.757324 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:21:32.757324 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:21:32.757324 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:21:32.757324 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:21:32.757324 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:21:32.757324 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:21:32.757324 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:21:32.757324 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:21:32.757324 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:21:32.758322 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:21:32.758322 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:21:32.758322 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:21:32.758322 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:21:32.758322 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:21:32.758322 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:21:32.758322 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:21:32.758322 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:21:32.758322 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:21:32.758322 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:21:33.425375 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:21:33.425375 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:21:33.425375 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:21:33.425375 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:21:33.425375 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:21:33.425375 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:21:33.425375 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:21:37.211869 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:21:37.211869 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:21:37.211869 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:21:37.589102 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:21:37.589102 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:21:37.589102 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:21:37.589102 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:21:37.589102 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:21:37.589102 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:21:37.589102 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:21:37.590104 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:21:37.590104 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:21:37.590104 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:21:43.534116 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:21:43.534116 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:21:43.534116 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:21:43.534116 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:21:43.534116 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:21:43.534116 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:21:43.534116 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:21:47.330722 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:21:47.330722 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:21:47.330722 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:21:47.694800 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:21:47.694800 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:21:47.694800 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:21:47.694800 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:21:47.694800 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:21:47.694800 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:21:47.694800 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:21:47.695801 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:21:47.695801 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:21:47.695801 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:21:53.545132 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:21:53.545132 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:21:53.545132 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:21:53.545132 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:21:53.545132 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:21:53.545132 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:21:53.545132 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:21:57.325284 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:21:57.325284 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:21:57.325284 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:21:57.691303 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:21:57.691303 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:21:57.691303 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:21:57.691303 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:21:57.691303 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:21:57.691303 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:21:57.691303 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:21:57.692301 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:21:57.692301 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:21:57.692301 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:21:57.692301 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:21:57.692301 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:21:57.692301 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:21:57.692301 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:21:57.692301 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:21:57.692301 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:22:02.935644 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:22:02.935644 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:22:02.936645 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:22:02.936645 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:22:02.936645 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:22:02.936645 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:22:02.936645 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:22:02.936645 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:22:02.936645 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:22:02.936645 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:22:02.936645 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:22:02.936645 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:22:02.936645 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:22:02.936645 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:22:02.937644 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:22:02.937644 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:22:02.937644 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:22:02.937644 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:22:02.937644 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:22:02.937644 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:22:02.937644 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:22:02.937644 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:22:02.937644 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:22:02.937644 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:22:02.937644 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:22:02.937644 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:22:02.937644 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:22:02.938644 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:22:02.938644 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:22:02.938644 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:22:02.938644 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:22:02.938644 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:22:02.938644 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:22:03.598899 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:22:03.598899 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:22:03.598899 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:22:03.598899 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:22:03.598899 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:22:03.598899 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:22:03.598899 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:22:07.383316 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:22:07.383316 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:22:07.383316 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:22:07.747303 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:22:07.747303 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:22:07.747303 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:22:07.747303 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:22:07.748054 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:22:07.748054 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:22:07.749058 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
********* 14:22:13.609752 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:22:13.609752 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:22:13.609752 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:22:13.609752 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:22:13.609752 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:22:13.609752 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:22:13.610754 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:22:17.392521 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:22:17.392521 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:22:17.392521 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:22:17.758792 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:22:17.758792 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:22:17.758792 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:22:17.758792 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:22:17.758792 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:22:17.758792 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:22:17.758792 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:22:17.759792 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:22:17.759792 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:22:17.759792 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (20 seconds)
********* 14:22:23.640092 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:22:23.640092 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:22:23.640092 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:22:23.640092 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:22:23.640092 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:22:23.640092 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:22:23.640092 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:22:27.424140 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:22:27.424140 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:22:27.424140 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:22:27.790746 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:22:27.791746 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:22:27.791746 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:22:27.791746 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:22:27.791746 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:22:27.791746 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:22:27.791746 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:22:27.791746 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:22:27.791746 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:22:27.791746 23976 AnalysisRobotApp.cpp:342] Attempting to reconnect devices (last attempt was 30 seconds ago)
********* 14:22:27.791746 23976 DeviceManager.cpp:133] Checking and connecting devices that need connection
********* 14:22:27.791746 23976 DeviceManager.cpp:155] Balance device 0 already connected
********* 14:22:27.791746 23976 DeviceManager.cpp:145] Attempting to connect balance device 1
********* 14:22:27.791746 23976 BalanceDriver.cpp:182] BalanceDriver Error: Connection failed: No error
********* 14:22:27.792747 23976 DeviceManager.cpp:147] Failed to connect balance device 1
********* 14:22:27.792747 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 0
********* 14:22:33.026415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM3
********* 14:22:33.026415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 0
********* 14:22:33.027415 23976 DeviceManager.cpp:176] Stirrer heater device 1 already connected
********* 14:22:33.027415 23976 DeviceManager.cpp:176] Stirrer heater device 2 already connected
********* 14:22:33.027415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 3
********* 14:22:33.027415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM6
********* 14:22:33.027415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 3
********* 14:22:33.027415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 4
********* 14:22:33.027415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM7
********* 14:22:33.027415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 4
********* 14:22:33.027415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 5
********* 14:22:33.028415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM8
********* 14:22:33.028415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 5
********* 14:22:33.028415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 6
********* 14:22:33.028415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM9
********* 14:22:33.028415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 6
********* 14:22:33.028415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 7
********* 14:22:33.028415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM10
********* 14:22:33.028415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 7
********* 14:22:33.028415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 8
********* 14:22:33.028415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM11
********* 14:22:33.028415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 8
********* 14:22:33.028415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 9
********* 14:22:33.029415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM12
********* 14:22:33.029415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 9
********* 14:22:33.029415 23976 DeviceManager.cpp:166] Attempting to connect stirrer heater device 10
********* 14:22:33.029415 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to open serial port: COM13
********* 14:22:33.029415 23976 DeviceManager.cpp:168] Failed to connect stirrer heater device 10
********* 14:22:33.029415 23976 DeviceManager.cpp:185] Attempting to connect moisture analyzer
********* 14:22:33.029415 23976 MoistureAnalyzerDriver.cpp:265] MoistureAnalyzerDriver Error: Connection failed: No error
********* 14:22:33.029415 23976 DeviceManager.cpp:187] Failed to connect moisture analyzer
********* 14:22:33.029415 23976 DeviceManager.cpp:221] Device connection summary: 3 already connected, 0 newly connected, 11 failed
********* 14:22:33.029415 23976 DeviceManager.cpp:229] Some devices failed to connect
********* 14:22:33.697497 23976 DeviceManager.cpp:332] Device balance2 not connected, status: DISCONNECTED
********* 14:22:33.697497 23976 DeviceManager.cpp:332] Device dosing not connected, status: FAILED
********* 14:22:33.697497 23976 DeviceManager.cpp:332] Device filter not connected, status: FAILED
********* 14:22:33.697497 23976 DeviceManager.cpp:332] Device frame not connected, status: FAILED
********* 14:22:33.697497 23976 DeviceManager.cpp:332] Device heater1 not connected, status: DISCONNECTED
********* 14:22:33.697497 23976 DeviceManager.cpp:332] Device heater10 not connected, status: DISCONNECTED
********* 14:22:33.697497 23976 DeviceManager.cpp:332] Device heater11 not connected, status: DISCONNECTED
********* 14:22:37.483947 23976 HeatingMagneticStirrerDriver.cpp:641] HeatingMagneticStirrerDriver Error: Failed to send read request after 3 retries
********* 14:22:37.483947 23976 HeatingMagneticStirrerDriver.cpp:492] Multiple consecutive read failures, setting device to FAULT state
********* 14:22:37.483947 23976 DeviceManager.cpp:332] Device heater2 not connected, status: FAILED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device heater4 not connected, status: DISCONNECTED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device heater5 not connected, status: DISCONNECTED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device heater6 not connected, status: DISCONNECTED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device heater7 not connected, status: DISCONNECTED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device heater8 not connected, status: DISCONNECTED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device heater9 not connected, status: DISCONNECTED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device icpEntry not connected, status: FAILED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device moistureBalance not connected, status: FAILED
********* 14:22:37.848723 23976 DeviceManager.cpp:332] Device pouring not connected, status: FAILED
********* 14:22:37.849723 23976 DeviceManager.cpp:332] Device repo not connected, status: FAILED
********* 14:22:37.849723 23976 DeviceManager.cpp:332] Device sampleExit not connected, status: FAILED
********* 14:22:37.849723 23976 DeviceManager.cpp:332] Device shaker not connected, status: FAILED
********* 14:22:37.849723 23976 DeviceManager.cpp:332] Device stir not connected, status: FAILED
********* 14:22:37.849723 23976 DeviceManager.cpp:332] Device volume not connected, status: FAILED
********* 14:22:37.849723 23976 DeviceManager.cpp:341] Device connection status: 3/25 connected
********* 14:22:37.849723 23976 AnalysisRobotApp.cpp:336] Some devices are not connected
********* 14:22:37.849723 23976 AnalysisRobotApp.cpp:346] Skipping reconnect attempt, too soon since last attempt (10 seconds)
